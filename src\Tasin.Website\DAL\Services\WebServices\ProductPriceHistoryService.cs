using AutoMapper;
using LinqKit;
using Tasin.Website.Common.CommonModels;
using Tasin.Website.Common.CommonModels.BaseModels;
using Tasin.Website.Common.Services;
using Tasin.Website.DAL.Interfaces;
using Tasin.Website.DAL.Services.WebInterfaces;
using Tasin.Website.Domains.DBContexts;
using Tasin.Website.Domains.Entitites;
using Tasin.Website.Models.ViewModels;
using Tasin.Website.Common.Helper;
using ClosedXML.Excel;

namespace Tasin.Website.DAL.Services.WebServices
{
    public class ProductPriceHistoryService : BaseService<ProductPriceHistoryService>, IProductPriceHistoryService
    {
        private readonly IMapper _mapper;
        private readonly IProductPriceHistoryRepository _productPriceHistoryRepository;
        private readonly IProductRepository _productRepository;

        public ProductPriceHistoryService(
            ILogger<ProductPriceHistoryService> logger,
            IUserRepository userRepository,
            IProductPriceHistoryRepository productPriceHistoryRepository,
            IProductRepository productRepository,
            IRoleRepository roleRepository,
            IHttpContextAccessor httpContextAccessor,
            IConfiguration configuration,
            ICurrentUserContext currentUserContext,
            SampleDBContext dbContext,
            IMapper mapper
            ) : base(logger, configuration, userRepository, roleRepository, httpContextAccessor, currentUserContext, dbContext)
        {
            _mapper = mapper;
            _productPriceHistoryRepository = productPriceHistoryRepository;
            _productRepository = productRepository;
        }

        public async Task<Acknowledgement<JsonResultPaging<List<ProductPriceHistoryViewModel>>>> GetPriceHistoryList(ProductPriceHistorySearchModel searchModel)
        {
            var response = new Acknowledgement<JsonResultPaging<List<ProductPriceHistoryViewModel>>>();
            try
            {
                var predicate = PredicateBuilder.New<ProductPriceHistory>(i => true);

                // Filter by ProductID if specified
                if (searchModel.ProductID.HasValue)
                {
                    predicate = predicate.And(i => i.ProductID == searchModel.ProductID.Value);
                }

                // Filter by date range
                if (searchModel.FromDate.HasValue)
                {
                    predicate = predicate.And(i => i.CreatedDate >= searchModel.FromDate.Value);
                }

                if (searchModel.ToDate.HasValue)
                {
                    var toDate = searchModel.ToDate.Value.Date.AddDays(1).AddTicks(-1); // End of day
                    predicate = predicate.And(i => i.CreatedDate <= toDate);
                }

                // Search by product name or product code
                if (!string.IsNullOrEmpty(searchModel.SearchString))
                {
                    var searchString = searchModel.SearchString.Trim().ToLower();
                    predicate = predicate.And(i => i.ProductName.ToLower().Contains(searchString));
                }

                var historyQuery = await _productPriceHistoryRepository.ReadOnlyRespository.GetWithPagingAsync(
                    filter: predicate,
                    orderBy: q => q.OrderByDescending(u => u.CreatedDate),
                    paging: new PagingParameters(searchModel.PageNumber, searchModel.PageSize)
                );

                var historyViewModels = _mapper.Map<List<ProductPriceHistoryViewModel>>(historyQuery.Data);

                // Load user names and product codes
                await LoadUserNamesAsync(historyViewModels);
                await LoadProductCodesAsync(historyViewModels);

                response.Data = new JsonResultPaging<List<ProductPriceHistoryViewModel>>
                {
                    Data = historyViewModels,
                    PageNumber = searchModel.PageNumber,
                    PageSize = searchModel.PageSize,
                    Total = historyQuery.TotalRecords
                };
                response.IsSuccess = true;
                return response;
            }
            catch (Exception ex)
            {
                response.ExtractMessage(ex);
                _logger.LogError($"GetPriceHistoryList: {ex.Message}");
                return response;
            }
        }

        public async Task<Acknowledgement<List<ProductPriceHistoryViewModel>>> GetPriceHistoryByProductId(int productId)
        {
            var response = new Acknowledgement<List<ProductPriceHistoryViewModel>>();
            try
            {
                var histories = await _productPriceHistoryRepository.GetByProductIdAsync(productId);
                var historyViewModels = _mapper.Map<List<ProductPriceHistoryViewModel>>(histories);

                // Load user names
                await LoadUserNamesAsync(historyViewModels);

                response.Data = historyViewModels;
                response.IsSuccess = true;
                return response;
            }
            catch (Exception ex)
            {
                response.ExtractMessage(ex);
                _logger.LogError($"GetPriceHistoryByProductId: {ex.Message}");
                return response;
            }
        }

        #region Helper Methods

        /// <summary>
        /// Load user names for price history view models
        /// </summary>
        private async Task LoadUserNamesAsync(List<ProductPriceHistoryViewModel> viewModels)
        {
            if (viewModels.Count == 0) return;

            var userIdList = viewModels
                .Select(i => i.CreatedBy)
                .Where(id => id > 0)
                .Distinct()
                .ToList();

            if (userIdList.Count == 0) return;

            var userList = await _userRepository.ReadOnlyRespository.GetAsync(i => userIdList.Contains(i.Id));
            var userLookup = userList.ToDictionary(u => u.Id, u => u.Name);

            foreach (var viewModel in viewModels)
            {
                if (userLookup.TryGetValue(viewModel.CreatedBy, out var createdByName))
                {
                    viewModel.CreatedByName = createdByName;
                }
            }
        }

        /// <summary>
        /// Load product codes for price history view models
        /// </summary>
        private async Task LoadProductCodesAsync(List<ProductPriceHistoryViewModel> viewModels)
        {
            if (viewModels.Count == 0) return;

            var productIdList = viewModels
                .Select(i => i.ProductID)
                .Distinct()
                .ToList();

            if (productIdList.Count == 0) return;

            var productList = await _productRepository.ReadOnlyRespository.GetAsync(i => productIdList.Contains(i.ID));
            var productLookup = productList.ToDictionary(p => p.ID, p => p.Code);

            foreach (var viewModel in viewModels)
            {
                if (productLookup.TryGetValue(viewModel.ProductID, out var productCode))
                {
                    viewModel.ProductCode = productCode;
                }
            }
        }

        #endregion

        /// <summary>
        /// Export product price history as Excel with tax calculation and selling price
        /// </summary>
        /// <param name="productId">Product ID</param>
        /// <param name="fromDate">Start date</param>
        /// <param name="toDate">End date</param>
        /// <returns>Excel file as byte array</returns>
        public async Task<byte[]> ExportPriceHistoryAsExcel(int productId, DateTime fromDate, DateTime toDate)
        {
            try
            {
                // Get price history data
                var searchModel = new ProductPriceHistorySearchModel
                {
                    ProductID = productId,
                    FromDate = fromDate,
                    ToDate = toDate.AddDays(1).AddSeconds(-1), // Include the entire end date
                    PageSize = int.MaxValue,
                    PageNumber = 1
                };

                var priceHistoryResult = await GetPriceHistoryList(searchModel);

                if (!priceHistoryResult.IsSuccess || priceHistoryResult.Data?.Data == null || !priceHistoryResult.Data.Data.Any())
                {
                    throw new Exception("Không tìm thấy dữ liệu lịch sử giá trong khoảng thời gian đã chọn");
                }

                var priceHistory = priceHistoryResult.Data.Data.OrderBy(p => p.CreatedDate).ToList();
                var productName = priceHistory.First().ProductName;

                // Create Excel workbook
                using (var workbook = ExcelHelper.CreateWorkbook())
                {
                    var worksheet = workbook.Worksheets.Add("Lịch sử biến động giá");
                    const decimal taxRate = 0.05m; // 5% tax rate

                    // Row 1: Product information
                    var productInfoCell = worksheet.Cell(1, 1);
                    ExcelHelper.SetCellValue(productInfoCell, $"Tên sản phẩm: {productName}");
                    productInfoCell.Style.Font.Bold = true;
                    productInfoCell.Style.Font.FontSize = 14;

                    // Row 2: Tax rate information (as editable value)
                    var taxLabelCell = worksheet.Cell(2, 1);
                    ExcelHelper.SetCellValue(taxLabelCell, "Mức thuế:");
                    taxLabelCell.Style.Font.Bold = true;
                    taxLabelCell.Style.Font.FontSize = 12;

                    var taxValueCell = worksheet.Cell(2, 2);
                    taxValueCell.Value = taxRate; // Set as decimal value (0.05)
                    taxValueCell.Style.NumberFormat.Format = "0%"; // Display as percentage
                    taxValueCell.Style.Font.Bold = true;
                    taxValueCell.Style.Font.FontSize = 12;
                    taxValueCell.Style.Fill.BackgroundColor = XLColor.LightYellow; // Highlight as editable

                    // Row 4: Headers for data table (leave row 3 empty for spacing)
                    var headers = new[]
                    {
                        "Thời gian biến động",
                        "Giá bán (VNĐ)",
                        "Thành tiền (VNĐ)"
                    };

                    // Apply header styling
                    for (int i = 0; i < headers.Length; i++)
                    {
                        var cell = worksheet.Cell(4, i + 1);
                        ExcelHelper.SetCellValue(cell, headers[i]);
                        cell.Style.Font.Bold = true;
                        cell.Style.Fill.BackgroundColor = XLColor.LightBlue;
                        cell.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;
                        cell.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                    }

                    // Populate data rows (starting from row 5)
                    int row = 5;

                    foreach (var item in priceHistory)
                    {
                        // Thời gian biến động
                        ExcelHelper.SetCellValue(worksheet.Cell(row, 1), item.CreatedDate.ToString("dd/MM/yyyy HH:mm:ss"));
                        worksheet.Cell(row, 1).Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;

                        // Giá bán
                        worksheet.Cell(row, 2).Value = item.Price;
                        worksheet.Cell(row, 2).Style.NumberFormat.Format = "#,##0";
                        worksheet.Cell(row, 2).Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Right;

                        // Thành tiền (formula: Giá bán * (1 + Mức thuế))
                        // Formula references: B{row} = Giá bán, $B$2 = Mức thuế
                        var formula = $"=B{row}*(1+$B$2)";
                        worksheet.Cell(row, 3).FormulaA1 = formula;
                        worksheet.Cell(row, 3).Style.NumberFormat.Format = "#,##0";
                        worksheet.Cell(row, 3).Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Right;

                        // Apply border to all cells in the row
                        for (int col = 1; col <= headers.Length; col++)
                        {
                            worksheet.Cell(row, col).Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                        }

                        row++;
                    }

                    // Auto-fit columns
                    worksheet.Columns().AdjustToContents();

                    // Set minimum column widths
                    worksheet.Column(1).Width = 25; // Thời gian biến động
                    worksheet.Column(2).Width = 20; // Giá bán
                    worksheet.Column(3).Width = 20; // Thành tiền

                    // Add instruction comment for users
                    var instructionCell = worksheet.Cell(row + 1, 1);
                    ExcelHelper.SetCellValue(instructionCell, "Lưu ý: Có thể thay đổi mức thuế ở ô B2 để tự động tính lại thành tiền");
                    instructionCell.Style.Font.Italic = true;
                    instructionCell.Style.Font.FontSize = 10;
                    instructionCell.Style.Font.FontColor = XLColor.Gray;
                    worksheet.Range(row + 1, 1, row + 1, headers.Length).Merge();

                    return ExcelHelper.SaveToByteArray(workbook);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"ExportPriceHistoryAsExcel: {ex.Message}");
                throw;
            }
        }
    }
}
