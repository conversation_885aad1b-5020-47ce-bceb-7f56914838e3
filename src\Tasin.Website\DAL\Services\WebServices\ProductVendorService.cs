using AutoMapper;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using Tasin.Website.Common.CommonModels;
using Tasin.Website.Common.CommonModels.BaseModels;
using Tasin.Website.Common.Helper;
using Tasin.Website.Common.Services;
using Tasin.Website.DAL.Interfaces;
using Tasin.Website.DAL.Repository;
using Tasin.Website.DAL.Services.WebInterfaces;
using Tasin.Website.Domains.DBContexts;
using Tasin.Website.Domains.Entitites;
using Tasin.Website.Models.SearchModels;
using Tasin.Website.Models.ViewModels;
using ClosedXML.Excel;
using Tasin.Website.Common.Enums;
using System.ComponentModel.DataAnnotations;

namespace Tasin.Website.DAL.Services.WebServices
{
    public class ProductVendorService : BaseService<ProductVendorService>, IProductVendorService
    {
        private readonly IProduct_VendorRepository _productVendorRepository;
        private readonly IProductRepository _productRepository;
        private readonly IVendorRepository _vendorRepository;
        private readonly IProductPriceHistoryRepository _productPriceHistoryRepository;
        private readonly IMapper _mapper;

        public ProductVendorService(
            ILogger<ProductVendorService> logger,
            IUserRepository userRepository,
            IProduct_VendorRepository productVendorRepository,
            IProductRepository productRepository,
            IVendorRepository vendorRepository,
            IProductPriceHistoryRepository productPriceHistoryRepository,
            IRoleRepository roleRepository,
            IHttpContextAccessor httpContextAccessor,
            IConfiguration configuration,
            ICurrentUserContext currentUserContext,
            SampleDBContext dbContext,
            IMapper mapper
            ) : base(logger, configuration, userRepository, roleRepository, httpContextAccessor, currentUserContext, dbContext)
        {
            _productVendorRepository = productVendorRepository;
            _productRepository = productRepository;
            _vendorRepository = vendorRepository;
            _productPriceHistoryRepository = productPriceHistoryRepository;
            _mapper = mapper;
        }

        public async Task<Acknowledgement<JsonResultPaging<List<ProductVendorViewModel>>>> GetProductVendorList(ProductVendorSearchModel searchModel)
        {
            var response = new Acknowledgement<JsonResultPaging<List<ProductVendorViewModel>>>();
            try
            {
                var predicate = PredicateBuilder.New<Product_Vendor>(true);

                if (searchModel.VendorId.HasValue)
                {
                    predicate = predicate.And(pv => pv.Vendor_ID == searchModel.VendorId.Value);
                }

                if (searchModel.ProductId.HasValue)
                {
                    predicate = predicate.And(pv => pv.Product_ID == searchModel.ProductId.Value);
                }

                if (searchModel.MinPrice.HasValue)
                {
                    predicate = predicate.And(pv => pv.Price >= searchModel.MinPrice.Value);
                }

                if (searchModel.MaxPrice.HasValue)
                {
                    predicate = predicate.And(pv => pv.Price <= searchModel.MaxPrice.Value);
                }

                if (searchModel.Priority.HasValue)
                {
                    predicate = predicate.And(pv => pv.Priority == searchModel.Priority.Value);
                }

                var productVendorQuery = await _productVendorRepository.ReadOnlyRespository.GetWithPagingAsync(
                new PagingParameters(searchModel.PageNumber, searchModel.PageSize),
                predicate,
                q => q.OrderBy(pv => pv.Vendor_ID).ThenBy(pv => pv.Priority ?? int.MaxValue),
                "Vendor,Product"
                );

                var productVendorViewModels = _mapper.Map<List<ProductVendorViewModel>>(productVendorQuery.Data);

                // Set display names
                foreach (var item in productVendorViewModels)
                {
                    var productVendor = productVendorQuery.Data.FirstOrDefault(pv =>
                        pv.Vendor_ID == item.Vendor_ID && pv.Product_ID == item.Product_ID);

                    if (productVendor != null)
                    {
                        item.VendorName = productVendor.Vendor?.Name;
                        item.ProductName = productVendor.Product?.Name;
                        item.ProductCode = productVendor.Product?.Code;
                    }
                }

                response.Data = new JsonResultPaging<List<ProductVendorViewModel>>
                {
                    Data = productVendorViewModels,
                    PageNumber = searchModel.PageNumber,
                    PageSize = searchModel.PageSize,
                    Total = productVendorQuery.TotalRecords
                };
                response.IsSuccess = true;
                return response;
            }
            catch (Exception ex)
            {
                response.ExtractMessage(ex);
                _logger.LogError($"GetProductVendorList: {ex.Message}");
                return response;
            }
        }

        public async Task<Acknowledgement<List<ProductVendorViewModel>>> GetProductsByVendorId(int vendorId)
        {
            var response = new Acknowledgement<List<ProductVendorViewModel>>();
            try
            {
                var productVendors = await _productVendorRepository.ReadOnlyRespository.GetAsync(
                    pv => pv.Vendor_ID == vendorId,
                    q => q.OrderBy(pv => pv.Priority ?? int.MaxValue),
                    null,
                    "Product",
                    e => new ProductVendorViewModel
                    {
                        Vendor_ID = e.Vendor_ID,
                        Product_ID = e.Product_ID,
                        UnitPrice = e.UnitPrice,
                        Priority = e.Priority,
                        Description = e.Description,
                        VendorName = e.Vendor.Name,
                        ProductName = e.Product.Name,
                        ProductCode = e.Product.Code,

                    }
                );

                response.Data = productVendors;
                response.IsSuccess = true;
                return response;
            }
            catch (Exception ex)
            {
                response.ExtractMessage(ex);
                _logger.LogError($"GetProductsByVendorId: {ex.Message}");
                return response;
            }
        }

        public async Task<Acknowledgement<List<ProductVendorViewModel>>> GetVendorsByProductId(int productId)
        {
            var response = new Acknowledgement<List<ProductVendorViewModel>>();
            try
            {
                var productVendors = await _productVendorRepository.ReadOnlyRespository.GetAsync(
                    pv => pv.Product_ID == productId,
                    q => q.OrderBy(pv => pv.Priority ?? int.MaxValue),
                    null,
                    "Vendor"
                );

                var viewModels = _mapper.Map<List<ProductVendorViewModel>>(productVendors);

                // Set display names
                foreach (var item in viewModels)
                {
                    var productVendor = productVendors.FirstOrDefault(pv =>
                        pv.Vendor_ID == item.Vendor_ID && pv.Product_ID == item.Product_ID);

                    if (productVendor?.Vendor != null)
                    {
                        item.VendorName = productVendor.Vendor.Name;
                    }
                }

                response.Data = viewModels;
                response.IsSuccess = true;
                return response;
            }
            catch (Exception ex)
            {
                response.ExtractMessage(ex);
                _logger.LogError($"GetVendorsByProductId: {ex.Message}");
                return response;
            }
        }

        public async Task<Acknowledgement<ProductVendorViewModel>> GetProductVendorById(int vendorId, int productId)
        {
            var response = new Acknowledgement<ProductVendorViewModel>();
            try
            {
                var productVendor = await _productVendorRepository.ReadOnlyRespository.FirstOrDefaultAsync(
                    pv => pv.Vendor_ID == vendorId && pv.Product_ID == productId,
                    "Vendor,Product"
                );

                if (productVendor == null)
                {
                    response.AddMessage("Không tìm thấy mối quan hệ sản phẩm-nhà cung cấp.");
                    return response;
                }

                var viewModel = _mapper.Map<ProductVendorViewModel>(productVendor);
                viewModel.VendorName = productVendor.Vendor?.Name;
                viewModel.ProductName = productVendor.Product?.Name;
                viewModel.ProductCode = productVendor.Product?.Code;

                response.Data = viewModel;
                response.IsSuccess = true;
                return response;
            }
            catch (Exception ex)
            {
                response.ExtractMessage(ex);
                _logger.LogError($"GetProductVendorById: {ex.Message}");
                return response;
            }
        }

        public async Task<Acknowledgement> CreateOrUpdateProductVendor(ProductVendorViewModel model)
        {
            var ack = new Acknowledgement();
            try
            {
                // Validate vendor exists
                var vendor = await _vendorRepository.Repository.FindAsync(model.Vendor_ID);
                if (vendor == null)
                {
                    ack.AddMessage("Không tìm thấy nhà cung cấp.");
                    return ack;
                }

                // Validate product exists
                var product = await _productRepository.Repository.FindAsync(model.Product_ID);
                if (product == null)
                {
                    ack.AddMessage("Không tìm thấy sản phẩm.");
                    return ack;
                }

                // Check if relationship already exists
                var existingProductVendor = await _productVendorRepository.Repository
                    .FirstOrDefaultAsync(pv => pv.Vendor_ID == model.Vendor_ID && pv.Product_ID == model.Product_ID);

                if (existingProductVendor != null)
                {
                    // Update existing relationship
                    existingProductVendor.Price = model.Price;
                    existingProductVendor.UnitPrice = model.UnitPrice;
                    existingProductVendor.Priority = model.Priority;
                    existingProductVendor.Description = model.Description;

                    await ack.TrySaveChangesAsync(res => res.UpdateAsync(existingProductVendor), _productVendorRepository.Repository);
                }
                else
                {
                    // Create new relationship
                    var newProductVendor = _mapper.Map<Product_Vendor>(model);
                    await ack.TrySaveChangesAsync(res => res.AddAsync(newProductVendor), _productVendorRepository.Repository);
                }

                // Cập nhật giá sản phẩm từ nhà cung cấp tốt nhất nếu thành công
                if (ack.IsSuccess)
                {
                    await UpdateProductPriceFromBestVendorAsync(model.Product_ID);
                }

                return ack;
            }
            catch (Exception ex)
            {
                ack.ExtractMessage(ex);
                _logger.LogError($"CreateOrUpdateProductVendor: {ex.Message}");
                return ack;
            }
        }

        /// <summary>
        /// Bulk update products for a vendor.
        /// Adds new products, updates existing ones, and removes products not in the new list.
        /// </summary>
        /// <param name="model">Bulk product-vendor data containing the complete list of products for the vendor</param>
        /// <returns>Acknowledgement with operation result</returns>
        public async Task<Acknowledgement> BulkAddProductsToVendor(BulkProductVendorViewModel model)
        {
            var ack = new Acknowledgement();
            try
            {
                // Validate vendor exists
                var vendor = await _vendorRepository.Repository.FindAsync(model.VendorId);
                if (vendor == null)
                {
                    ack.AddMessage("Không tìm thấy nhà cung cấp.");
                    return ack;
                }

                // Get current product IDs from the request (empty list is allowed for deletion scenario)
                var newProductIds = model.Products?.Select(p => p.Product_ID).ToList() ?? new List<int>();

                // Validate that all requested products exist if any are provided
                if (newProductIds.Any())
                {
                    var products = await _productRepository.ReadOnlyRespository.GetAsync(p => newProductIds.Contains(p.ID));
                    if (products.Count != newProductIds.Count)
                    {
                        ack.AddMessage("Một số sản phẩm không tồn tại trong hệ thống.");
                        return ack;
                    }
                }

                // Get all existing product-vendor relationships for this vendor
                var existingProductVendors = await _productVendorRepository.ReadOnlyRespository.GetAsync(
                    pv => pv.Vendor_ID == model.VendorId);

                var existingProductIds = existingProductVendors.Select(pv => pv.Product_ID).ToHashSet();

                // Determine which products to delete (exist in DB but not in new list)
                var productsToDelete = existingProductIds.Except(newProductIds).ToList();

                // Determine which products to add or update
                var newProductVendors = new List<Product_Vendor>();
                var updateProductVendors = new List<Product_Vendor>();

                // Process products in the new list
                if (model.Products != null)
                {
                    foreach (var productItem in model.Products)
                    {
                        if (existingProductIds.Contains(productItem.Product_ID))
                        {
                            // Update existing relationship
                            var existing = existingProductVendors.First(pv => pv.Product_ID == productItem.Product_ID);
                            existing.UnitPrice = productItem.UnitPrice;
                            existing.Priority = productItem.Priority;
                            existing.Description = productItem.Description;
                            updateProductVendors.Add(existing);
                        }
                        else
                        {
                            // Create new relationship
                            var newProductVendor = new Product_Vendor
                            {
                                Vendor_ID = model.VendorId,
                                Product_ID = productItem.Product_ID,
                                UnitPrice = productItem.UnitPrice,
                                Priority = productItem.Priority,
                                Description = productItem.Description
                            };
                            newProductVendors.Add(newProductVendor);
                        }
                    }
                }

                // Delete products that are no longer in the list
                var productVendorsToDelete = existingProductVendors
                    .Where(pv => productsToDelete.Contains(pv.Product_ID))
                    .ToList();

                // Save changes in transaction
                using var transaction = await _productVendorRepository.Repository.BeginTransactionAsync();
                try
                {
                    // Delete removed products
                    if (productVendorsToDelete.Any())
                    {
                        await _productVendorRepository.Repository.DeleteRangeAsync(productVendorsToDelete);
                    }

                    // Add new products
                    if (newProductVendors.Any())
                    {
                        await _productVendorRepository.Repository.AddRangeAsync(newProductVendors);
                    }

                    // Update existing products
                    if (updateProductVendors.Any())
                    {
                        await _productVendorRepository.Repository.UpdateRangeAsync(updateProductVendors);
                    }

                    await _productVendorRepository.Repository.SaveChangesAsync();
                    await transaction.CommitAsync();
                    ack.IsSuccess = true;

                    // Cập nhật giá cho tất cả sản phẩm đã thay đổi (bao gồm cả sản phẩm bị xóa)
                    var allAffectedProductIds = newProductIds.Concat(productsToDelete).Distinct();
                    foreach (var productId in allAffectedProductIds)
                    {
                        await UpdateProductPriceFromBestVendorAsync(productId);
                    }
                }
                catch (Exception)
                {
                    await transaction.RollbackAsync();
                    throw;
                }

                return ack;
            }
            catch (Exception ex)
            {
                ack.ExtractMessage(ex);
                _logger.LogError($"BulkAddProductsToVendor: {ex.Message}");
                return ack;
            }
        }

        public async Task<Acknowledgement> DeleteProductVendor(int vendorId, int productId)
        {
            var ack = new Acknowledgement();
            try
            {
                var productVendor = await _productVendorRepository.Repository
                    .FirstOrDefaultAsync(pv => pv.Vendor_ID == vendorId && pv.Product_ID == productId);

                if (productVendor == null)
                {
                    ack.AddMessage("Không tìm thấy mối quan hệ sản phẩm-nhà cung cấp.");
                    return ack;
                }

                await ack.TrySaveChangesAsync(res => res.DeleteAsync(productVendor), _productVendorRepository.Repository);

                // Cập nhật giá sản phẩm từ nhà cung cấp tốt nhất còn lại nếu xóa thành công
                if (ack.IsSuccess)
                {
                    await UpdateProductPriceFromBestVendorAsync(productId);
                }

                return ack;
            }
            catch (Exception ex)
            {
                ack.ExtractMessage(ex);
                _logger.LogError($"DeleteProductVendor: {ex.Message}");
                return ack;
            }
        }

        public async Task<Acknowledgement> BulkDeleteProductsFromVendor(int vendorId, List<int> productIds)
        {
            var ack = new Acknowledgement();
            try
            {
                if (productIds == null || !productIds.Any())
                {
                    ack.AddMessage("Danh sách sản phẩm không được để trống.");
                    return ack;
                }

                var productVendors = await _productVendorRepository.ReadOnlyRespository.GetAsync(
                    pv => pv.Vendor_ID == vendorId && productIds.Contains(pv.Product_ID));

                if (!productVendors.Any())
                {
                    ack.AddMessage("Không tìm thấy mối quan hệ sản phẩm-nhà cung cấp nào để xóa.");
                    return ack;
                }

                await _productVendorRepository.Repository.DeleteRangeAsync(productVendors);
                await _productVendorRepository.Repository.SaveChangesAsync();

                ack.IsSuccess = true;
                return ack;
            }
            catch (Exception ex)
            {
                ack.ExtractMessage(ex);
                _logger.LogError($"BulkDeleteProductsFromVendor: {ex.Message}");
                return ack;
            }
        }

        public async Task<Acknowledgement<ProductVendorExcelImportResult>> ImportProductVendorsFromExcel(IFormFile file)
        {
            var response = new Acknowledgement<ProductVendorExcelImportResult>();
            var result = new ProductVendorExcelImportResult();

            try
            {
                // Validate file
                if (file == null || file.Length == 0)
                {
                    response.AddMessage("Vui lòng chọn file Excel để import.");
                    return response;
                }

                var fileExtension = Path.GetExtension(file.FileName).ToLower();
                if (!FileHelper.ValidateFileExt(EFileType.Excel, fileExtension))
                {
                    response.AddMessage("File không đúng định dạng Excel (.xlsx, .xls, .xlsm, .csv).");
                    return response;
                }

                // Read Excel file
                var importModels = new List<ProductVendorExcelImportModel>();
                using (var stream = new MemoryStream())
                {
                    await file.CopyToAsync(stream);
                    stream.Position = 0;

                    using (var workbook = ExcelHelper.CreateWorkbook(stream))
                    {
                        var worksheet = workbook.Worksheet(1);
                        var rows = worksheet.RowsUsed().Skip(1); // Skip header row

                        int rowNumber = 2; // Start from row 2 (after header)
                        foreach (var row in rows)
                        {
                            // Extract vendor code from dropdown format "CODE - Name" or just "CODE"
                            var vendorCodeRaw = ExcelHelper.GetCellStringValue(row.Cell(1));
                            var vendorCode = ExtractCodeFromDropdownValue(vendorCodeRaw);

                            // Extract product code from dropdown format "CODE - Name" or just "CODE"
                            var productCodeRaw = ExcelHelper.GetCellStringValue(row.Cell(2));
                            var productCode = ExtractCodeFromDropdownValue(productCodeRaw);

                            var importModel = new ProductVendorExcelImportModel
                            {
                                RowNumber = rowNumber,
                                VendorCode = vendorCode,
                                ProductCode = productCode,
                                UnitPrice = ParseDecimal(ExcelHelper.GetCellStringValue(row.Cell(3))),
                                Priority = ParseInt(ExcelHelper.GetCellStringValue(row.Cell(4))),
                                Description = ExcelHelper.GetCellStringValue(row.Cell(5))
                            };

                            // Skip empty rows
                            if (string.IsNullOrWhiteSpace(importModel.VendorCode) &&
                                string.IsNullOrWhiteSpace(importModel.ProductCode))
                            {
                                rowNumber++;
                                continue;
                            }

                            importModels.Add(importModel);
                            rowNumber++;
                        }
                    }
                }

                result.TotalRows = importModels.Count;

                if (!importModels.Any())
                {
                    response.AddMessage("File Excel không có dữ liệu để import.");
                    return response;
                }

                // Validate and process each row
                var validImportModels = new List<ProductVendorExcelImportModel>();
                foreach (var importModel in importModels)
                {
                    var validationErrors = ValidateProductVendorImportModel(importModel);
                    if (validationErrors.Any())
                    {
                        result.FailedRows++;
                        result.Errors.Add(new ProductVendorExcelImportError
                        {
                            RowNumber = importModel.RowNumber,
                            VendorCode = importModel.VendorCode,
                            ProductCode = importModel.ProductCode,
                            ErrorMessages = validationErrors
                        });
                    }
                    else
                    {
                        validImportModels.Add(importModel);
                    }
                }

                // Get all vendor codes and product codes for batch lookup
                var vendorCodes = validImportModels.Select(m => m.VendorCode).Distinct().ToList();
                var productCodes = validImportModels.Select(m => m.ProductCode).Distinct().ToList();

                // Batch lookup vendors and products
                var vendors = await _vendorRepository.ReadOnlyRespository.GetAsync(
                    v => vendorCodes.Contains(v.Code));
                var products = await _productRepository.ReadOnlyRespository.GetAsync(
                    p => productCodes.Contains(p.Code));

                var vendorDict = vendors.ToDictionary(v => v.Code, v => v);
                var productDict = products.ToDictionary(p => p.Code, p => p);

                // Process valid import models
                foreach (var importModel in validImportModels)
                {
                    try
                    {
                        // Check if vendor and product exist
                        if (!vendorDict.TryGetValue(importModel.VendorCode, out var vendor))
                        {
                            result.FailedRows++;
                            result.Errors.Add(new ProductVendorExcelImportError
                            {
                                RowNumber = importModel.RowNumber,
                                VendorCode = importModel.VendorCode,
                                ProductCode = importModel.ProductCode,
                                ErrorMessages = new List<string> { $"Không tìm thấy nhà cung cấp với mã: {importModel.VendorCode}" }
                            });
                            continue;
                        }

                        if (!productDict.TryGetValue(importModel.ProductCode, out var product))
                        {
                            result.FailedRows++;
                            result.Errors.Add(new ProductVendorExcelImportError
                            {
                                RowNumber = importModel.RowNumber,
                                VendorCode = importModel.VendorCode,
                                ProductCode = importModel.ProductCode,
                                ErrorMessages = new List<string> { $"Không tìm thấy sản phẩm với mã: {importModel.ProductCode}" }
                            });
                            continue;
                        }

                        // Check if relationship already exists
                        var existingProductVendor = await _productVendorRepository.ReadOnlyRespository
                            .FirstOrDefaultAsync(pv => pv.Vendor_ID == vendor.ID && pv.Product_ID == product.ID);

                        if (existingProductVendor != null)
                        {
                            // Update existing relationship
                            existingProductVendor.UnitPrice = importModel.UnitPrice;
                            existingProductVendor.Priority = importModel.Priority;
                            existingProductVendor.Description = importModel.Description;

                            await _productVendorRepository.Repository.UpdateAsync(existingProductVendor);
                        }
                        else
                        {
                            // Create new relationship
                            var newProductVendor = new Product_Vendor
                            {
                                Vendor_ID = vendor.ID,
                                Product_ID = product.ID,
                                UnitPrice = importModel.UnitPrice,
                                Priority = importModel.Priority,
                                Description = importModel.Description
                            };

                            await _productVendorRepository.Repository.AddAsync(newProductVendor);
                        }

                        result.SuccessfulRows++;
                    }
                    catch (Exception ex)
                    {
                        result.FailedRows++;
                        result.Errors.Add(new ProductVendorExcelImportError
                        {
                            RowNumber = importModel.RowNumber,
                            VendorCode = importModel.VendorCode,
                            ProductCode = importModel.ProductCode,
                            ErrorMessages = new List<string> { $"Lỗi xử lý: {ex.Message}" }
                        });
                        _logger.LogError($"Error processing row {importModel.RowNumber}: {ex.Message}");
                    }
                }

                // Save changes
                await DbContext.SaveChangesAsync();

                response.Data = result;
                response.IsSuccess = true;
                return response;
            }
            catch (Exception ex)
            {
                response.ExtractMessage(ex);
                _logger.LogError($"ImportProductVendorsFromExcel: {ex.Message}");
                return response;
            }
        }

        public async Task<byte[]> GenerateProductVendorExcelTemplate()
        {
            try
            {
                // Get data for dropdowns
                var vendors = await _vendorRepository.ReadOnlyRespository.GetAsync(v => v.IsActive);
                var products = await _productRepository.ReadOnlyRespository.GetAsync(p => p.IsActive);

                using (var workbook = ExcelHelper.CreateWorkbook())
                {
                    // Create main data sheet
                    var dataSheet = workbook.Worksheets.Add("Hàng hóa - Nhà cung cấp");

                    // Set headers
                    var headers = new[]
                    {
                        "Nhà cung cấp (*)",
                        "Sản phẩm (*)",
                        "Đơn giá",
                        "Độ ưu tiên",
                        "Mô tả"
                    };

                    for (int i = 0; i < headers.Length; i++)
                    {
                        var cell = dataSheet.Cell(1, i + 1);
                        ExcelHelper.SetCellValue(cell, headers[i]);
                        cell.Style.Font.Bold = true;
                        cell.Style.Fill.BackgroundColor = XLColor.LightBlue;
                        cell.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;
                    }

                    // Prepare dropdown data
                    var vendorCodes = vendors
                        .OrderBy(v => v.Name)
                        .Select(v => $"{v.Code} - {v.Name}")
                        .ToList();

                    var productCodes = products
                        .OrderBy(p => p.Name)
                        .Select(p => $"{p.Code} - {p.Name}")
                        .ToList();

                    // Auto-fit columns
                    dataSheet.Columns().AdjustToContents();

                    // Create reference sheet for dropdowns
                    var referenceSheet = workbook.Worksheets.Add("Danh sách tham chiếu");

                    // Add headers
                    ExcelHelper.SetCellValue(referenceSheet.Cell(1, 1), "Nhà cung cấp");
                    ExcelHelper.SetCellValue(referenceSheet.Cell(1, 2), "Sản phẩm");

                    // Style headers
                    for (int col = 1; col <= 2; col++)
                    {
                        referenceSheet.Cell(1, col).Style.Font.Bold = true;
                        referenceSheet.Cell(1, col).Style.Fill.BackgroundColor = XLColor.LightGray;
                    }

                    // Add vendors data
                    for (int i = 0; i < vendorCodes.Count; i++)
                    {
                        ExcelHelper.SetCellValue(referenceSheet.Cell(i + 2, 1), vendorCodes[i]);
                    }

                    // Add products data
                    for (int i = 0; i < productCodes.Count; i++)
                    {
                        ExcelHelper.SetCellValue(referenceSheet.Cell(i + 2, 2), productCodes[i]);
                    }

                    // Auto-fit columns in reference sheet
                    referenceSheet.Columns().AdjustToContents();

                    // Create dropdown validations

                    // Vendor dropdown (column A) - Required
                    if (vendorCodes.Any())
                    {
                        var vendorRange = $"$A$2:$A${vendorCodes.Count + 1}";
                        ExcelHelper.CreateDropdownValidationFromSheet(
                            dataSheet,
                            "A2:A1000",
                            "Danh sách tham chiếu",
                            vendorRange,
                            allowBlank: false // Vendor is required
                        );
                    }

                    // Product dropdown (column B) - Required
                    if (productCodes.Any())
                    {
                        var productRange = $"$B$2:$B${productCodes.Count + 1}";
                        ExcelHelper.CreateDropdownValidationFromSheet(
                            dataSheet,
                            "B2:B1000",
                            "Danh sách tham chiếu",
                            productRange,
                            allowBlank: false // Product is required
                        );
                    }

                    // Hide the reference sheet
                    // referenceSheet.Visibility = XLWorksheetVisibility.Hidden;

                    // Create sample data sheet
                    var sampleSheet = workbook.Worksheets.Add("Dữ liệu mẫu");

                    // Add sample headers
                    for (int i = 0; i < headers.Length; i++)
                    {
                        var cell = sampleSheet.Cell(1, i + 1);
                        ExcelHelper.SetCellValue(cell, headers[i]);
                        cell.Style.Font.Bold = true;
                        cell.Style.Fill.BackgroundColor = XLColor.LightBlue;
                        cell.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;
                    }

                    // Add sample data using actual data from dropdowns
                    var sampleData = new[]
                    {
                        new {
                            VendorCode = vendorCodes.FirstOrDefault() ?? "NCC001 - Nhà cung cấp mẫu",
                            ProductCode = productCodes.FirstOrDefault() ?? "SP001 - Sản phẩm mẫu",
                            UnitPrice = "50000",
                            Priority = "1",
                            Description = "Nhà cung cấp chính"
                        },
                        new {
                            VendorCode = vendorCodes.Skip(1).FirstOrDefault() ?? "NCC002 - Nhà cung cấp mẫu 2",
                            ProductCode = productCodes.FirstOrDefault() ?? "SP001 - Sản phẩm mẫu",
                            UnitPrice = "52000",
                            Priority = "2",
                            Description = "Nhà cung cấp phụ"
                        },
                        new {
                            VendorCode = vendorCodes.FirstOrDefault() ?? "NCC001 - Nhà cung cấp mẫu",
                            ProductCode = productCodes.Skip(1).FirstOrDefault() ?? "SP002 - Sản phẩm mẫu 2",
                            UnitPrice = "75000",
                            Priority = "1",
                            Description = ""
                        }
                    };

                    for (int i = 0; i < sampleData.Length; i++)
                    {
                        var row = i + 2;
                        ExcelHelper.SetCellValue(sampleSheet.Cell(row, 1), sampleData[i].VendorCode);
                        ExcelHelper.SetCellValue(sampleSheet.Cell(row, 2), sampleData[i].ProductCode);
                        ExcelHelper.SetCellValue(sampleSheet.Cell(row, 3), sampleData[i].UnitPrice);
                        ExcelHelper.SetCellValue(sampleSheet.Cell(row, 4), sampleData[i].Priority);
                        ExcelHelper.SetCellValue(sampleSheet.Cell(row, 5), sampleData[i].Description);
                    }

                    // Auto-fit columns
                    sampleSheet.Columns().AdjustToContents();

                    // Create instruction sheet
                    var instructionSheet = workbook.Worksheets.Add("Hướng dẫn");
                    var instructions = new[]
                    {
                        "HƯỚNG DẪN IMPORT HÀNG HÓA - NHÀ CUNG CẤP",
                        "",
                        "1. Các cột bắt buộc (có dấu *):",
                        "   - Nhà cung cấp: Chọn từ dropdown danh sách nhà cung cấp",
                        "   - Sản phẩm: Chọn từ dropdown danh sách sản phẩm",
                        "",
                        "2. Các cột tùy chọn:",
                        "   - Đơn giá: Giá đơn vị của sản phẩm từ nhà cung cấp",
                        "   - Độ ưu tiên: Số thứ tự ưu tiên (số nhỏ hơn = ưu tiên cao hơn)",
                        "   - Mô tả: Ghi chú về mối quan hệ nhà cung cấp - sản phẩm",
                        "",
                        "3. Sử dụng Dropdown:",
                        "   - Cột 'Nhà cung cấp' có dropdown chứa tất cả nhà cung cấp trong hệ thống",
                        "   - Cột 'Sản phẩm' có dropdown chứa tất cả sản phẩm trong hệ thống",
                        "   - Chọn từ dropdown để tránh lỗi nhập liệu",
                        "",
                        "4. Lưu ý:",
                        "   - Nếu mối quan hệ đã tồn tại, hệ thống sẽ cập nhật thông tin",
                        "   - Nếu chưa tồn tại, hệ thống sẽ tạo mới",
                        "   - Chỉ chọn từ dropdown, không nhập tay để tránh lỗi",
                        "   - Xem tab 'Dữ liệu mẫu' để tham khảo format"
                    };

                    for (int i = 0; i < instructions.Length; i++)
                    {
                        var cell = instructionSheet.Cell(i + 1, 1);
                        ExcelHelper.SetCellValue(cell, instructions[i]);
                        if (i == 0)
                        {
                            cell.Style.Font.Bold = true;
                            cell.Style.Font.FontSize = 14;
                        }
                    }

                    instructionSheet.Columns().AdjustToContents();

                    // Set the main data sheet as active
                    dataSheet.SetTabActive();

                    using (var stream = new MemoryStream())
                    {
                        workbook.SaveAs(stream, ExcelHelper.GetDefaultSaveOptions());
                        return stream.ToArray();
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"GenerateProductVendorExcelTemplate: {ex.Message}");
                throw;
            }
        }

        #region Helper Methods

        private static decimal? ParseDecimal(string value)
        {
            if (string.IsNullOrWhiteSpace(value))
                return null;

            if (decimal.TryParse(value.Replace(",", ""), out decimal result))
                return result;

            return null;
        }

        private static int? ParseInt(string value)
        {
            if (string.IsNullOrWhiteSpace(value))
                return null;

            if (int.TryParse(value, out int result))
                return result;

            return null;
        }

        private List<string> ValidateProductVendorImportModel(ProductVendorExcelImportModel model)
        {
            var errors = new List<string>();

            // Validate using data annotations
            var validationContext = new ValidationContext(model);
            var validationResults = new List<ValidationResult>();

            if (!Validator.TryValidateObject(model, validationContext, validationResults, true))
            {
                errors.AddRange(validationResults.Select(vr => vr.ErrorMessage ?? "Validation error"));
            }

            // Additional business validations
            if (model.Priority.HasValue && model.Priority.Value < 0)
            {
                errors.Add("Độ ưu tiên phải là số không âm");
            }

            if (model.UnitPrice.HasValue && model.UnitPrice.Value < 0)
            {
                errors.Add("Đơn giá phải là số không âm");
            }

            return errors;
        }

        /// <summary>
        /// Extract code from dropdown value format "CODE - Name" or just "CODE"
        /// </summary>
        /// <param name="dropdownValue">Value from Excel dropdown</param>
        /// <returns>Extracted code</returns>
        private static string ExtractCodeFromDropdownValue(string dropdownValue)
        {
            if (string.IsNullOrWhiteSpace(dropdownValue))
                return string.Empty;

            var value = dropdownValue.Trim();

            // Extract code from format "CODE - Name" or just "CODE"
            if (value.Contains(" - "))
            {
                value = value.Split(" - ")[0].Trim();
            }

            return value;
        }

        /// <summary>
        /// Update product price from the best vendor (highest priority and lowest price)
        /// </summary>
        /// <param name="productId">Product ID</param>
        /// <returns>True if price was updated, False otherwise</returns>
        private async Task<bool> UpdateProductPriceFromBestVendorAsync(int productId)
        {
            try
            {
                // Lấy sản phẩm hiện tại
                var product = await _productRepository.Repository.FindAsync(productId);
                if (product == null)
                {
                    _logger.LogWarning($"Product with ID {productId} not found");
                    return false;
                }

                // Lấy nhà cung cấp tốt nhất cho sản phẩm này
                var bestVendor = await _productVendorRepository.GetBestVendorForProductAsync(productId);
                if (bestVendor == null || !bestVendor.UnitPrice.HasValue || bestVendor.UnitPrice.Value <= 0)
                {
                    _logger.LogInformation($"No valid vendor found for product {productId}");
                    return false;
                }

                // Kiểm tra xem có thay đổi giá không
                var newInputPrice = bestVendor.UnitPrice.Value;
                var oldDefaultPrice = product.DefaultPrice;

                // Cập nhật InputPrice
                product.InputPrice = newInputPrice;

                // Tính lại DefaultPrice từ InputPrice và ProfitMargin
                if (product.ProfitMargin.HasValue && product.ProfitMargin.Value >= 0)
                {
                    product.DefaultPrice = newInputPrice * (1 + product.ProfitMargin.Value / 100);
                }
                else
                {
                    // Nếu không có ProfitMargin, giữ nguyên DefaultPrice hoặc set bằng InputPrice
                    if (!product.DefaultPrice.HasValue || product.DefaultPrice.Value <= 0)
                    {
                        product.DefaultPrice = newInputPrice;
                    }
                }

                // Cập nhật thông tin audit
                product.UpdatedDate = DateTime.Now;
                product.UpdatedBy = CurrentUserId;

                // Lưu thay đổi
                await _productRepository.Repository.UpdateAsync(product);

                // Ghi lịch sử thay đổi giá nếu có thay đổi
                if (product.DefaultPrice.HasValue &&
                    (!oldDefaultPrice.HasValue || Math.Abs(oldDefaultPrice.Value - product.DefaultPrice.Value) > 0.01m))
                {
                    await CreatePriceHistoryAsync(productId, product.Name, product.DefaultPrice.Value);
                }

                _logger.LogInformation($"Updated product {productId} price from vendor. InputPrice: {newInputPrice}, DefaultPrice: {product.DefaultPrice}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"UpdateProductPriceFromBestVendor: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Create price history record
        /// </summary>
        /// <param name="productId">Product ID</param>
        /// <param name="productName">Product name</param>
        /// <param name="price">New price</param>
        private async Task CreatePriceHistoryAsync(int productId, string productName, decimal price)
        {
            try
            {
                var priceHistory = new ProductPriceHistory
                {
                    ProductID = productId,
                    ProductName = productName,
                    Price = price,
                    CreatedDate = DateTime.Now,
                    CreatedBy = CurrentUserId
                };

                await _productPriceHistoryRepository.Repository.AddAsync(priceHistory);
            }
            catch (Exception ex)
            {
                _logger.LogError($"CreatePriceHistory: {ex.Message}");
                // Không throw exception để không ảnh hưởng đến luồng chính
            }
        }

        #endregion

    }
}
