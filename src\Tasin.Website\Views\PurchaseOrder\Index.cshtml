﻿@{
    ViewData["Title"] = "Danh sách đơn hàng PO";
}

@section Styles {
            <link href="~/css/kendo-grid-common.css" rel="stylesheet" asp-append-version="true" />
            <link href="~/css/toolbar-common.css" rel="stylesheet" asp-append-version="true" />
            <link href="~/css/form-common.css" rel="stylesheet" />


            <link href="~/css/grid-mobile.css" rel="stylesheet" asp-append-version="true" />
            <style>
                #itemsGrid{
                    overflow-x: auto !important;
                }

            </style>
}

<div>
    <h4 class="demo-section wide title">@ViewData["Title"]</h4>
    <div id="divContent">
        <div id="gridId">
        </div>
    </div>
    <div id="window"></div>
    <div id="dialog"></div>
</div>

<script type="text/javascript">
    let gridId = "#gridId";

    // Set default date range to last 1 week (user preference)
    var today = EndDay();
    var oneWeekAgo = new Date();
    oneWeekAgo.setDate(today.getDate() - 7);

    // Common function to calculate total estimated material
    function calculateTotalEstimatedMaterial(quantity, lossRate) {
        var qty = quantity || 0;
        var loss = lossRate || 0;
        return (100 + loss) * qty / 100;
    }

    function getSearchModel() {
        let searchString = $("#searchString").val();
        let customerId = $("#customerId").data("kendoDropDownList")?.value();
        let status = $("#status").data("kendoDropDownList")?.value();
        let dateFromPicker = $("#dateFrom").data("kendoDatePicker");
        let dateToPicker = $("#dateTo").data("kendoDatePicker");

        // Get date values from Kendo DatePickers
        let dateFrom = null;
        let dateTo = null;

        if (dateFromPicker && dateFromPicker.value()) {
            dateFrom = kendo.toString(dateFromPicker.value(),"yyyy-MM-ddTHH:mm:ss");
        }

        if (dateToPicker && dateToPicker.value()) {
            let dateToObj = dateToPicker.value();
            // Set to end of day
            dateToObj.setHours(23, 59, 59, 999);
            dateTo = kendo.toString(dateToObj,"yyyy-MM-ddTHH:mm:ss");
        }

        return {
            searchString: searchString || null,
            customer_ID: customerId && customerId !== "" ? parseInt(customerId) : null,
            status: status || null,
            dateFrom: dateFrom || kendo.toString(oneWeekAgo,"yyyy-MM-ddTHH:mm:ss"),
            dateTo: dateTo || kendo.toString(today,"yyyy-MM-ddTHH:mm:ss")
        };
    }

    async function ExportExcel() {
        let dataSheet1 = [
            {
                cells: [
                    { value: "Mã đơn hàng", textAlign: "center", background: "#428dd8" },
                    { value: "Khách hàng", textAlign: "center", background: "#428dd8" },
                    { value: "Trạng thái", textAlign: "center", background: "#428dd8" },
                    { value: "Tổng tiền chưa thuế", textAlign: "center", background: "#428dd8" },
                    { value: "Tổng tiền", textAlign: "center", background: "#428dd8" },
                    { value: "Ngày cập nhật", textAlign: "center", background: "#428dd8" },
                    { value: "Người tạo", textAlign: "center", background: "#428dd8" }
                ]
            }];

        var searchModel = getSearchModel();
        let postData = {
            ...searchModel,
            pageSize: 999999999,
            pageNumber: 1
        }
        let dataSourcePO = null;
        var response = await ajax("GET", "/PurchaseOrder/GetPurchaseOrderList", postData, (poResponse) => {
            dataSourcePO = poResponse.data.data;
        }, null, false);
        if (dataSourcePO == null) return;

        for (let index = 0; index < dataSourcePO.length; index++) {
            dataSheet1.push({
                cells: [
                    { value: dataSourcePO[index].code },
                    { value: dataSourcePO[index].customerName },
                    { value: dataSourcePO[index].statusName },
                    { value: dataSourcePO[index].totalPriceNoTax },
                    { value: dataSourcePO[index].totalPrice },
                    { value: (dataSourcePO[index].updatedDate || dataSourcePO[index].createdDate) ? kendo.toString(kendo.parseDate(dataSourcePO[index].updatedDate || dataSourcePO[index].createdDate), "dd/MM/yyyy HH:mm") : '' },
                    { value: dataSourcePO[index].createdByName }
                ]
            })
        }

        var workbook = new kendo.ooxml.Workbook({
            sheets: [
                {
                    name: "Danh sách đơn hàng PO",
                    columns: [
                        { autoWidth: true }, { autoWidth: true }, { autoWidth: true },
                        { autoWidth: true }, { autoWidth: true }, { autoWidth: true }, { autoWidth: true }
                    ],
                    rows: dataSheet1,
                }
            ]
        });
        kendo.saveAs({
            dataURI: workbook.toDataURL(),
            fileName: "Danh sách đơn hàng PO _ " + kendo.toString(new Date(), "dd_MM_yyyy__HH_mm_ss") + ".xlsx"
        });
    }



    async function editPurchaseOrder(id) {
        var response = ajax("GET", "/PurchaseOrder/GetPurchaseOrderById", { purchaseOrderId: id }, (response) => {
            renderPurchaseOrderForm(response.data);
        }, null, false);
    }

    function deletePurchaseOrder(id) {
        $('#dialog').kendoConfirm({
            title: "THÔNG BÁO XÓA ĐƠN HÀNG PO",
            content: "Bạn có chắc chắn xóa đơn hàng PO này không?",
            size: "medium",
            messages: {
                okText: "Đồng ý",
                cancel: "Hủy"
            },
        }).data("kendoConfirm").open().result.done(function () {
            var response = ajax("DELETE", "/PurchaseOrder/DeletePurchaseOrderById/" + id, {}, () => {
                $(gridId).data("kendoGrid").dataSource.filter({});
            });
        })

        $("#window").after("<div id='dialog'></div>");
    }

    function cancelPurchaseOrder(id) {
        $('#dialog').kendoConfirm({
            title: "THÔNG BÁO HỦY ĐƠN HÀNG PO",
            content: "Bạn có chắc chắn hủy đơn hàng PO này không?",
            size: "medium",
            messages: {
                okText: "Đồng ý",
                cancel: "Hủy"
            },
        }).data("kendoConfirm").open().result.done(function () {
            var response = ajax("PUT", "/PurchaseOrder/CancelPurchaseOrderById/" + id, {}, () => {
                $(gridId).data("kendoGrid").dataSource.filter({});
            });
        })

        $("#window").after("<div id='dialog'></div>");
    }

    function confirmPurchaseOrder(id) {
        $('#dialog').kendoConfirm({
            title: "XÁC NHẬN ĐƠN HÀNG PO",
            content: "Bạn có chắc chắn muốn xác nhận đơn hàng PO này không?",
            size: "medium",
            messages: {
                okText: "Đồng ý",
                cancel: "Hủy"
            },
        }).data("kendoConfirm").open().result.done(function () {
            // Get current purchase order data first
            ajax("GET", "/PurchaseOrder/GetPurchaseOrderById", { purchaseOrderId: id }, function (response) {
                if (response.isSuccess && response.data) {
                    var purchaseOrder = response.data;
                    // Update status to Confirmed
                    purchaseOrder.status = "Confirmed";

                    // Call update API
                    ajax("PUT", "/PurchaseOrder/UpdatePurchaseOrder", purchaseOrder, function (updateResponse) {
                        if (updateResponse.isSuccess) {
                            showSuccessMessages(["Xác nhận đơn hàng thành công!"]);
                            $(gridId).data("kendoGrid").dataSource.filter({});
                            CountPO();
                        } else {
                            showErrorMessages(updateResponse.errorMessageList);
                        }
                    }, null, false);
                } else {
                    showErrorMessages(response.errorMessageList || ["Không thể lấy thông tin đơn hàng"]);
                }
            }, null, false);
        })

        $("#window").after("<div id='dialog'></div>");
    }

    function viewPurchaseOrderDetail(id) {
        var response = ajax("GET", "/PurchaseOrder/GetPurchaseOrderById", { purchaseOrderId: id }, (response) => {
            showPurchaseOrderDetail(response.data);
        }, null, false);
    }

    function showPurchaseOrderDetail(data) {
        let myWindow = $("#window");

        // Determine status class for badge styling
        let statusClass = "";
        switch (data.status) {
            case "New":
            case "0":
                statusClass = "new"; break;
            case "Confirmed":
            case "1":
                statusClass = "confirmed"; break;
            case "Sent":
            case "2":
                statusClass = "sent"; break;
            case "Cancel":
            case "3":
                statusClass = "cancel"; break;
            default: statusClass = "new";
        }

        let detailHtml = `
            <div class="po-detail-container">
                <h6><strong>Chi tiết đơn hàng: ${data.code || ''}</strong></h6>
                <div class="po-info-row">
                    <div class="po-info-col">
                        <p><strong>Mã đơn hàng:</strong> ${data.code || ''}</p>
                        <p><strong>Khách hàng:</strong> ${data.customerName || ''}</p>
                        <p><strong>Tổng tiền chưa thuế:</strong> ${kendo.toString(data.totalPriceNoTax || 0, "n0")} VNĐ</p>
                        <p><strong>Số TK khách hàng:</strong> ${data.customerAccountNumber || 'Chưa có thông tin'}</p>
                        <p><strong>Hình thức thanh toán:</strong> ${data.paymentMethodName || 'Chưa có thông tin'}</p>
                    </div>
                    <div class="po-info-col">
                        <p><strong>Trạng thái:</strong> <span class="status-badge ${statusClass}">${data.statusName || ''}</span></p>
                        <p><strong>Tổng tiền:</strong> ${kendo.toString(data.totalPrice || 0, "n0")} VNĐ</p>
                        <p><strong>Ngày tạo:</strong> ${data.createdDate ? kendo.toString(kendo.parseDate(data.createdDate), "dd/MM/yyyy HH:mm") : ''}</p>
                        <p><strong>Tên ngân hàng:</strong> ${data.customerBankName || 'Chưa có thông tin'}</p>
                    </div>
                </div>
                <div class="po-items-detail">
                    <h6><strong>Danh sách sản phẩm:</strong></h6>
                    <div id="itemsGrid"></div>
                </div>
            </div>
        `;

        $("#window").html(detailHtml);

        // Initialize items grid
        $("#itemsGrid").kendoGrid({
            dataSource: {
                data: data.purchaseOrderItems || [],
                schema: {
                    model: {
                        fields: {
                            quantity: { type: "number" },
                            price: { type: "number" },
                            taxRate: { type: "number" },
                            lossRate: { type: "number" },
                            additionalCost: { type: "number" },
                            processingFee: { type: "number" }
                        }
                    }
                }
            },
            columns: [
                { field: "productName", title: "Sản phẩm", width: "200px" },
                { field: "quantity", title: "Số lượng", width: "100px", template: "#= kendo.toString(quantity, 'n2') #", attributes: { style: "text-align: center;" } },
                { field: "unitName", title: "Đơn vị", width: "80px", attributes: { style: "text-align: center;" } },
                { field: "price", title: "Đơn giá", width: "120px", template: "#= kendo.toString(price, 'n0') # VNĐ", attributes: { style: "text-align: right;" } },
                { field: "lossRate", title: "Hao hụt (%)", width: "100px", template: "#= lossRate ? kendo.toString(lossRate, 'n1') + '%' : '' #", attributes: { style: "text-align: center;" } },
                {
                    title: "Tổng ước tính nguyên liệu",
                    width: "150px",
                    template: function(dataItem) {
                        var totalEstimatedMaterial = calculateTotalEstimatedMaterial(dataItem.quantity, dataItem.lossRate);
                        return kendo.toString(totalEstimatedMaterial, 'n2');
                    },
                    attributes: { style: "text-align: center;" }
                },
                { field: "additionalCost", title: "Chi phí thêm", width: "110px", template: "#= additionalCost ? kendo.toString(additionalCost, 'n0') + ' VNĐ' : '' #", attributes: { style: "text-align: right;" } },
                { field: "taxRate", title: "Thuế suất (%)", width: "100px", template: "#= taxRate ? kendo.toString(taxRate, 'n1') + '%' : '' #", attributes: { style: "text-align: center;" } },
                { field: "processingFee", title: "Phí gia công", width: "120px", template: "#= processingFee ? kendo.toString(processingFee, 'n0') + ' VNĐ' : '' #", attributes: { style: "text-align: right;" } },
                {
                    title: "Thành tiền",
                    width: "150px",
                    template: function(dataItem) {
                        var baseAmount = (dataItem.quantity || 0) * (dataItem.price || 0);
                        var totalProcessingFee = (dataItem.quantity || 0) * (dataItem.processingFee || 0);
                        var totalBeforeTax = baseAmount + (dataItem.additionalCost || 0) + totalProcessingFee;
                        var taxAmount = totalBeforeTax * ((dataItem.taxRate || 0) / 100);
                        var finalAmount = totalBeforeTax + taxAmount;
                        return kendo.toString(finalAmount, 'n0') + ' VNĐ';
                    },
                    attributes: { style: "text-align: right;" }
                },
                { field: "note", title: "Ghi chú", width: "200px" }
            ],
            pageable: false,
            scrollable: true,
            height: 400,
            dataBound: function (e) {
                loadMobile("#itemsGrid");
            },
        });

        function remove() {
            setTimeout(() => {
                if ($(".k-window #window").length > 0) {
                    $("#window").parent().remove();
                    $(gridId).after("<div id='window'></div>");
                }
            }, 200)
        }

        myWindow.kendoWindow({
            width: "1400px",
            height: "800px",
            title: "CHI TIẾT ĐƠN HÀNG PO",
            visible: false,
            actions: ["Close"],
            resizable: true,
            draggable: true,
            modal: true,
            close: function (e) {
                remove();
            },
        }).data("kendoWindow").center();
        myWindow.data("kendoWindow").open();
    }



    function generateInvoice(id) {
        window.open(`/PurchaseOrder/PreviewInvoice?purchaseOrderId=${id}`, '_blank');
    }

    function exportInvoicePdf(id) {
        window.open(`/PurchaseOrder/ExportInvoicePdf?purchaseOrderId=${id}`, '_blank');
    }

    function exportInvoiceExcel(id) {
        window.open(`/PurchaseOrder/ExportInvoiceExcel?purchaseOrderId=${id}`, '_blank');
    }



    function exportDeliveryNotePdf(id) {
        window.open(`/PurchaseOrder/ExportDeliveryNotePdf?purchaseOrderId=${id}`, '_blank');
    }

    function exportPOExcel(id) {
        window.open(`/PurchaseOrder/ExportPOExcel?purchaseOrderId=${id}`, '_blank');
    }

    function bulkExportPOExcel() {
        var grid = $(gridId).data("kendoGrid");
        var selectedRows = grid.select();

        if (selectedRows.length === 0) {
            showErrorMessages(["Vui lòng chọn ít nhất một đơn hàng để xuất Excel"]);
            return;
        }

        var selectedIds = [];
        selectedRows.each(function() {
            var dataItem = grid.dataItem(this);
            selectedIds.push(dataItem.id);
        });

        // Create form to submit POST request for bulk export
        var form = $('<form>', {
            'method': 'POST',
            'action': '/PurchaseOrder/BulkExportPOExcel',
            'target': '_blank'
        });

        // Add CSRF token
        var token = $('input[name="__RequestVerificationToken"]').val();
        if (token) {
            form.append($('<input>', {
                'type': 'hidden',
                'name': '__RequestVerificationToken',
                'value': token
            }));
        }

        // Add selected IDs
        for (var i = 0; i < selectedIds.length; i++) {
            form.append($('<input>', {
                'type': 'hidden',
                'name': 'purchaseOrderIds',
                'value': selectedIds[i]
            }));
        }

        // Submit form
        $('body').append(form);
        form.submit();
        form.remove();

        showSuccessMessages([`Đang xuất ${selectedIds.length} đơn hàng...`]);
    }

    function toggleInvoiceDropdown(button, id) {
        // Close all existing dropdowns
        $('.invoice-dropdown-overlay').remove();

        // Create dropdown menu HTML
        var dropdownHtml = `
            <div class="invoice-dropdown-overlay" style="
                position: fixed;
                z-index: 99999;
                background: white;
                border: 1px solid #e9ecef;
                border-radius: 6px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                min-width: 180px;
                padding: 8px 0;
                font-size: 14px;
            ">
                <a href="javascript:void(0)" onclick="generateInvoice(${id}); $('.invoice-dropdown-overlay').remove();"
                   style="display: block; padding: 8px 16px; color: #212529; text-decoration: none; cursor: pointer;">
                    <i class="fas fa-eye" style="margin-right: 8px; width: 16px;"></i>Xem trước
                </a>
                <a href="javascript:void(0)" onclick="exportInvoicePdf(${id}); $('.invoice-dropdown-overlay').remove();"
                   style="display: block; padding: 8px 16px; color: #212529; text-decoration: none; cursor: pointer;">
                    <i class="fas fa-file-pdf" style="margin-right: 8px; width: 16px; color: #dc3545;"></i>Xuất PDF
                </a>
                <a href="javascript:void(0)" onclick="exportInvoiceExcel(${id}); $('.invoice-dropdown-overlay').remove();"
                   style="display: block; padding: 8px 16px; color: #212529; text-decoration: none; cursor: pointer;">
                    <i class="fas fa-file-excel" style="margin-right: 8px; width: 16px; color: #198754;"></i>Xuất Excel
                </a>

                <hr style="margin: 4px 0; border: none; border-top: 1px solid #e9ecef;">
                <a href="javascript:void(0)" onclick="exportPOExcel(${id}); $('.invoice-dropdown-overlay').remove();"
                   style="display: block; padding: 8px 16px; color: #212529; text-decoration: none; cursor: pointer;">
                    <i class="fas fa-file-excel" style="margin-right: 8px; width: 16px; color: #198754;"></i>Xuất PO Excel
                </a>
                <a href="javascript:void(0)" onclick="exportDeliveryNotePdf(${id}); $('.invoice-dropdown-overlay').remove();"
                   style="display: block; padding: 8px 16px; color: #212529; text-decoration: none; cursor: pointer;">
                    <i class="fas fa-truck" style="margin-right: 8px; width: 16px; color: #fd7e14;"></i>Phiếu giao hàng
                </a>
            </div>
        `;

        // Get button position
        var buttonOffset = $(button).offset();
        var buttonHeight = $(button).outerHeight();
        var buttonWidth = $(button).outerWidth();

        // Calculate position
        var top = buttonOffset.top + buttonHeight + 4;
        var left = buttonOffset.left;

        // Adjust if menu would go off screen
        var windowWidth = $(window).width();
        var windowHeight = $(window).height();

        // Create temporary element to measure dropdown size
        var $tempDropdown = $(dropdownHtml).css({
            'position': 'fixed',
            'top': '-9999px',
            'left': '-9999px',
            'visibility': 'hidden'
        });
        $('body').append($tempDropdown);

        var menuWidth = $tempDropdown.outerWidth();
        var menuHeight = $tempDropdown.outerHeight();
        $tempDropdown.remove();

        // Adjust position if needed
        if (left + menuWidth > windowWidth - 20) {
            left = buttonOffset.left + buttonWidth - menuWidth;
        }

        if (top + menuHeight > windowHeight - 20) {
            top = buttonOffset.top - menuHeight - 4;
        }

        // Create and position the dropdown
        var $dropdown = $(dropdownHtml).css({
            'top': top + 'px',
            'left': left + 'px'
        });

        // Add hover effects
        $dropdown.find('a').hover(
            function () {
                $(this).css('background-color', '#f8f9fa');
            },
            function () {
                $(this).css('background-color', 'transparent');
            }
        );

        // Append to body
        $('body').append($dropdown);

        // Add fade in effect
        $dropdown.hide().fadeIn(150);
    }

    // Close dropdown when clicking outside
    $(document).on('click', function (e) {
        if (!$(e.target).closest('.dropdown').length && !$(e.target).closest('.invoice-dropdown-overlay').length && !$(e.target).closest('.btn-invoice').length) {
            $('.invoice-dropdown-overlay').remove();
        }
    });

    function renderPurchaseOrderForm(data = null) {
        var isEdit = data !== null && data !== undefined;
        var windowTitle = isEdit ? "CHỈNH SỬA ĐƠN HÀNG PO" : "THÊM MỚI ĐƠN HÀNG PO";

        let formHtml = `
            <div class="">
                <div class="po-header-section">
                    <h5>Thông tin đơn hàng</h5>
                    <div class="header-grid-3col">
                        <div class="header-col-3-1">
                            <div class="form-group">
                                <label>Khách hàng (*)</label>
                                <select id="po-customerId" class="form-control"></select>
                            </div>
                        </div>
                        <div class="header-col-3-1">
                            <div class="form-group">
                                <label>Trạng thái</label>
                                <select id="po-status" class="form-control"></select>
                            </div>
                        </div>
                        <div class="header-col-3-1">
                            <div class="form-group">
                                <label>Mã đơn hàng</label>
                                <input type="text" id="po-code" class="form-control" readonly placeholder="Tự động tạo"/>
                                <input type="hidden" id="po-id" value="0"/>
                            </div>
                        </div>
                        <div class="header-col-3-1">
                            <div class="form-group">
                                <label>Tổng tiền chưa thuế</label>
                                <input type="text" id="po-totalPriceNoTax" class="form-control" readonly/>
                            </div>
                        </div>
                        <div class="header-col-3-1">
                            <div class="form-group">
                                <label>Tổng tiền</label>
                                <input type="text" id="po-totalPrice" class="form-control" readonly/>
                            </div>
                        </div>
                        <div class="header-col-3-1">
                            <div class="form-group">
                                <label>Số TK khách hàng</label>
                                <input type="text" id="po-customerAccountNumber" class="form-control" placeholder="Nhập số tài khoản"/>
                            </div>
                        </div>
                        <div class="header-col-3-1">
                            <div class="form-group">
                                <label>Tên ngân hàng</label>
                                <input type="text" id="po-customerBankName" class="form-control" placeholder="Nhập tên ngân hàng"/>
                            </div>
                        </div>
                        <div class="header-col-3-1">
                            <div class="form-group">
                                <label>Hình thức thanh toán</label>
                                <select id="po-paymentMethod" class="form-control">
                                    <option value="">-- Chọn hình thức --</option>
                                </select>
                            </div>
                        </div>
                        <div class="header-col-3-1">
                            <!-- Empty column for spacing -->
                        </div>
                    </div>
                </div>

                <hr>

                <!-- Item Input Section -->
                <div class="po-item-section">
                    <h5>Thêm sản phẩm vào đơn hàng</h5>
                    <div class="item-input-grid">
                        <!-- Row 1: Thông tin cơ bản sản phẩm -->
                        <div class="row gx-0 row-gap-2 w-100">
                            <div class="col-xl-3 col-lg-3 col-md-6 col-sm-6 col-12">
                                <div class="pe-1">
                                    <label>Sản phẩm (*)</label>
                                    <select id="item-productId" class=""></select>
                                </div>
                            </div>
                            <div class="col-xl-3 col-lg-3 col-md-6 col-sm-6 col-12">
                                <div class="pe-1">
                                    <label>Số lượng (*)</label>
                                    <input type="number" id="item-quantity" class="" step="0.01" min="0"/>
                                </div>
                            </div>
                            <div class="col-xl-3 col-lg-3 col-md-6 col-sm-6 col-12 d-sm-mobile-none">
                                <div class="pe-1">
                                    <label>Đơn vị</label>
                                    <input type="text" id="item-unitName" class="" readonly/>
                                </div>
                            </div>
                            <div class="col-xl-3 col-lg-3 col-md-6 col-sm-6 col-12">
                                <div class="pe-1">
                                    <label>Đơn giá</label>
                                    <input type="number" id="item-price" class="" step="0.01" min="0"/>
                                </div>
                            </div>
                        </div>

                        <!-- Row 2: Thông tin chi phí và tỷ lệ -->
                        <div class="row gx-0 row-gap-2 w-100 d-sm-mobile-none">
                            <div class="col-xl-3 col-lg-3 col-md-6 col-sm-6 col-12">
                                <div class="pe-1">
                                    <label>Hao hụt (%)</label>
                                    <input type="number" id="item-lossRate" class="" step="0.1" min="0" max="100"/>
                                </div>
                            </div>
                            <div class="col-xl-3 col-lg-3 col-md-6 col-sm-6 col-12">
                                <div class="pe-1">
                                    <label>Phí gia công</label>
                                    <input type="number" id="item-processingFee" class="" step="0.01" min="0"/>
                                </div>
                            </div>
                            <div class="col-xl-3 col-lg-3 col-md-6 col-sm-6 col-12">
                                <div class="pe-1">
                                    <label>Chi phí thêm</label>
                                    <input type="number" id="item-additionalCost" class="" step="0.01" min="0" value="0"/>
                                </div>
                            </div>
                            <div class="col-xl-3 col-lg-3 col-md-6 col-sm-6 col-12">
                                <div class="pe-1">
                                    <label>Thuế (%)</label>
                                    <input type="number" id="item-taxRate" class="" step="0.1" min="0" max="100"/>
                                </div>
                            </div>
                        </div>

                        <!-- Row 3: Thông tin bổ sung -->
                        <div class="row gx-0 row-gap-2 w-100 d-sm-mobile-none">
                            <div class="col-xl-3 col-lg-3 col-md-12 col-sm-12 col-12">
                                <div class="pe-1">
                                    <label>Thành tiền</label>
                                    <input type="text" id="item-totalAmount" class=" total-amount-highlight" readonly/>
                                </div>
                            </div>
                            <div class="col-xl-9 col-lg-9 col-md-12 col-sm-12 col-12">
                                <div class="pe-1">
                                    <label>Ghi chú</label>
                                    <input type="text" id="item-note" class=""/>
                                </div>
                            </div>
                        </div>

                        <!-- Row 4: Button row -->
                        <div class="grid-row-button d-flex justify-content-end">
                            <button type="button" id="addItemBtn" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Thêm sản phẩm
                            </button>
                        </div>
                    </div>
                </div>

                <hr>

                <!-- Items List Section -->
                <div class="po-items-section">
                    <h5>Danh sách sản phẩm</h5>
                    <div id="itemsGrid"></div>
                </div>

                <!-- Form Actions -->
                <div class="form-actions">
                    <button type="button" id="savePOBtn" class="btn btn-success">
                        <i class="fas fa-save"></i> ${isEdit ? 'Cập nhật' : 'Lưu'}
                    </button>
                    <button type="button" id="cancelPOBtn" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Hủy
                    </button>
                </div>
            </div>
        `;

        let myWindow = $("#window");
        myWindow.html(formHtml);

        function remove() {
            setTimeout(() => {
                if ($(".k-window #window").length > 0) {
                    $("#window").parent().remove();
                    $(gridId).after("<div id='window'></div>");
                }
            }, 200)
        }

        myWindow.kendoWindow({
            width: "1400px",
            height: "800px",
            title: windowTitle,
            visible: false,
            actions: ["Close"],
            resizable: true,
            draggable: true,
            modal: true,
            close: function (e) {
                remove();
            },
        }).data("kendoWindow").center();
        myWindow.data("kendoWindow").open();

        // Initialize form after window opens
        setTimeout(function () {
            initializePurchaseOrderForm(data);
        }, 100);
    }

    function initializePurchaseOrderForm(data) {
        var isEdit = data !== null && data !== undefined;
        var poItems = [];

        // Store edit mode for later use
        window.purchaseOrderEditMode = isEdit;

        // Initialize Customer Dropdown
        $("#po-customerId").kendoDropDownList({
            dataTextField: "text",
            dataValueField: "value",
            optionLabel: "-- Chọn khách hàng --",
            filter: filterCustom,
            template: function (dataItem) {
                if (!dataItem || !dataItem.text) {
                    return "N/A";
                }
                return dataItem.text;
            },
            valueTemplate: function (dataItem) {
                if (!dataItem || !dataItem.text) {
                    return "-- Chọn khách hàng --";
                }
                return dataItem.text;
            },
            change: function (e) {
                var selectedCustomerId = this.value();
                if (selectedCustomerId && selectedCustomerId !== "") {
                    // Load customer details to determine tax rate
                    loadCustomerDetails(selectedCustomerId);
                    // Enable product selection when customer is selected
                    enableProductSelection();
                } else {
                    // Clear stored customer data
                    $("#po-customerId").removeData("customerType");
                    // Disable product selection when no customer is selected
                    disableProductSelection();
                }
            },
            dataSource: {
                transport: {
                    read: {
                        url: "/Common/GetDataOptionsDropdown",
                        data: { type: "Customer" }
                    }
                },
                schema: {
                    parse: function (response) {
                        if (response && response.isSuccess && response.data) {
                            return response.data.filter(function (item) {
                                return item && item.text && item.value !== undefined;
                            });
                        }
                        return [];
                    }
                }
            }
        });

        // Initialize Status Dropdown
        $("#po-status").kendoDropDownList({
            dataTextField: "text",
            dataValueField: "dataRaw",
            optionLabel: "-- Chọn trạng thái --",
            template: function (dataItem) {
                if (!dataItem || !dataItem.text) {
                    return "N/A";
                }
                return dataItem.text;
            },
            valueTemplate: function (dataItem) {
                if (!dataItem || !dataItem.text) {
                    return "-- Chọn trạng thái --";
                }
                return dataItem.text;
            },
            dataSource: {
                transport: {
                    read: {
                        url: "/Common/GetDataOptionsDropdown",
                        data: { type: "POStatus" }
                    }
                },
                schema: {
                    parse: function (response) {
                        if (response && response.isSuccess && response.data) {
                            return response.data.filter(function (item) {
                                return item && item.text && item.dataRaw !== undefined;
                            });
                        }
                        return [];
                    }
                }
            },
            dataBound: function () {
                // Always disable status dropdown - status can only be changed via action buttons
                this.enable(false);

                // Set default status to "New" for new orders
                if (!isEdit) {
                    // Find the item with "New" status (by enum value)
                    var dataSource = this.dataSource;
                    var newStatusItem = null;
                    for (var i = 0; i < dataSource.data().length; i++) {
                        var item = dataSource.data()[i];
                        // Compare by enum value - look for "New"
                        if (item.dataRaw === "New") {
                            newStatusItem = item;
                            break;
                        }
                    }

                    if (newStatusItem) {
                        this.value(newStatusItem.dataRaw);
                    }
                }
            }
        });

        // Initialize Payment Method Dropdown
        $("#po-paymentMethod").kendoDropDownList({
            dataTextField: "text",
            dataValueField: "value",
            optionLabel: "-- Chọn hình thức --",
            template: function (dataItem) {
                if (!dataItem || !dataItem.text) {
                    return "N/A";
                }
                return dataItem.text;
            },
            valueTemplate: function (dataItem) {
                if (!dataItem || !dataItem.text) {
                    return "-- Chọn hình thức --";
                }
                return dataItem.text;
            },
            dataSource: {
                transport: {
                    read: {
                        url: "/Common/GetDataOptionsDropdown",
                        data: { type: "PaymentMethod" }
                    }
                },
                schema: {
                    parse: function (response) {
                        if (response && response.isSuccess && response.data) {
                            return response.data.filter(function (item) {
                                return item && item.text && item.value !== undefined;
                            });
                        }
                        return [];
                    }
                }
            }
        });

        // Initialize Product Dropdown with Dynamic Search
        $("#item-productId").kendoComboBox({
            dataTextField: "text",
            dataValueField: "value",
            placeholder: "Vui lòng chọn sản phẩm...",
            clearButton: false,
            suggest: true,
            filter: "contains", // Enable filtering to trigger server-side search
            minLength: 1,
            highlightFirst: true,
            template: function (dataItem) {
                if (!dataItem || !dataItem.text) {
                    return "N/A";
                }
                return dataItem.text;
            },
            change: function (e) {
                // Check if customer is selected first
                var customerId = $("#po-customerId").data("kendoDropDownList").value();
                if (!customerId || customerId === "") {
                    showErrorMessages(["Vui lòng chọn khách hàng trước khi chọn sản phẩm"]);
                    this.value("");
                    return;
                }

                var selectedProduct = this.dataItem();
                if (selectedProduct && selectedProduct.value) {
                    // Load product details
                    loadProductDetails(selectedProduct.value);
                } else {
                    // Clear product details if no valid selection
                    clearProductFields();
                }
            },
            select: function (e) {
                var selectedProduct = this.dataItem(e.item.index());
                if (selectedProduct && selectedProduct.value) {
                    // Load product details
                    loadProductDetails(selectedProduct.value);
                }
            },

            dataSource: {
                transport: {
                    read: {
                        url: "/Common/GetDataOptionsDropdown",
                        data: {
                            type: "Product",
                            searchString: "" // Default empty search - loads first products
                        }
                    },
                    parameterMap: function (data, type) {
                        if (type === "read") {
                            return {
                                type: "Product",
                                searchString: data.filter && data.filter.filters && data.filter.filters.length > 0
                                    ? data.filter.filters[0].value
                                    : ""
                            };
                        }
                        return data;
                    }
                },
                serverFiltering: true,
                schema: {
                    parse: function (response) {
                        if (response && response.isSuccess && response.data) {
                            return response.data.filter(function (item) {
                                return item && item.text && item.value !== undefined;
                            });
                        }
                        return [];
                    }
                }
            },
            height: 300,
            animation: {
                open: {
                    effects: "fadeIn",
                    duration: 200
                }
            }
        });

        $("#po-code").kendoTextBox({});
        $("#po-totalPriceNoTax").kendoTextBox({});
        $("#po-totalPrice").kendoTextBox({});
        $("#po-customerAccountNumber").kendoTextBox({});
        $("#po-customerBankName").kendoTextBox({});

        $("#item-quantity").kendoNumericTextBox({format: "{0:N0}"});
        $("#item-unitName").kendoTextBox({});
        $("#item-price").kendoNumericTextBox({format: "{0:N0}"});
        $("#item-lossRate").kendoNumericTextBox({format: "{0:N1}"});
        $("#item-processingFee").kendoNumericTextBox({format: "{0:N0}"});
        $("#item-additionalCost").kendoNumericTextBox({format: "{0:N0}"});
        $("#item-taxRate").kendoNumericTextBox({format: "{0:N0}"});
        $("#item-totalAmount").kendoTextBox({});
        $("#item-note").kendoTextBox({});




        // Initialize Items Grid
        $("#itemsGrid").kendoGrid({
            dataSource: {
                data: poItems,
                schema: {
                    model: {
                        id: "tempId",
                        fields: {
                            tempId: { type: "string" },
                            productId: { type: "number" },
                            productName: { type: "string" },
                            quantity: { type: "number" },
                            unitName: { type: "string" },
                            price: { type: "number" },
                            lossRate: { type: "number" },
                            additionalCost: { type: "number" },
                            taxRate: { type: "number" },
                            processingFee: { type: "number" },
                            totalAmount: { type: "number" },
                            note: { type: "string" }
                        }
                    }
                }
            },
            columns: [
                { field: "productName", title: "Sản phẩm", width: "180px" },
                { field: "quantity", title: "Số lượng", width: "90px", template: "#= kendo.toString(quantity, 'n2') #" },
                { field: "unitName", title: "Đơn vị", width: "80px" },
                { field: "price", title: "Đơn giá", width: "110px", template: "#= kendo.toString(price, 'n0') # VNĐ" },
                { field: "lossRate", title: "Hao hụt (%)", width: "100px", template: "#= lossRate ? kendo.toString(lossRate, 'n1') + '%' : '' #" },
                {
                    title: "Tổng ước tính nguyên liệu",
                    width: "140px",
                    template: function(dataItem) {
                        var totalEstimatedMaterial = calculateTotalEstimatedMaterial(dataItem.quantity, dataItem.lossRate);
                        return kendo.toString(totalEstimatedMaterial, 'n2');
                    }
                },
                { field: "additionalCost", title: "Chi phí thêm", width: "110px", template: "#= additionalCost ? kendo.toString(additionalCost, 'n0') + ' VNĐ' : '' #" },
                { field: "taxRate", title: "Thuế (%)", width: "90px", template: "#= taxRate ? kendo.toString(taxRate, 'n1') + '%' : '' #" },
                { field: "processingFee", title: "Phí gia công", width: "110px", template: "#= processingFee ? kendo.toString(processingFee, 'n0') + ' VNĐ' : '' #" },
                { field: "totalAmount", title: "Thành tiền", width: "130px", template: "#= kendo.toString(totalAmount, 'n0') # VNĐ" },
                { field: "note", title: "Ghi chú", width: "150px" },
                {
                    title: "Thao tác",
                    width: "80px",
                    template: '<button class="btn btn-sm btn-danger" onclick="removeItem(\'#= tempId #\')"><i class="fas fa-trash"></i></button>'
                }
            ],
            pageable: false,
            scrollable: { virtual: false },
            height: 300,
            dataBound: function (e) {
                loadMobile("#itemsGrid");
            },
        });

        // Event Handlers
        $("#addItemBtn").click(function () {
            addItemToGrid();
        });

        $("#savePOBtn").click(function () {
            savePurchaseOrder();
        });

        $("#cancelPOBtn").click(function () {
            $("#window").data("kendoWindow").close();
        });

        // Initialize form state based on mode
        if (isEdit) {
            // Disable customer dropdown in edit mode
            var customerDropdown = $("#po-customerId").data("kendoDropDownList");
            if (customerDropdown) {
                customerDropdown.enable(false);
            }

            // Load data if editing
            setTimeout(function () {
                loadPurchaseOrderData(data);
            }, 200);
        } else {
            // Disable product selection initially for new orders
            disableProductSelection();
        }
    }

    function InitGrid() {
        let htmlToolbar = `
                <div id='toolbar' style=''  class='w-100 d-flex flex-column'>
                       <div class="row gx-0 row-gap-2 w-100">
                            <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6 col-12">
                                <div class="pe-1">
                                    <label for="searchString">Tìm kiếm:</label>
                                    <input type="text" class="w-100" id="searchString" placeholder="Nhập mã đơn hàng..."/>
                                </div>
                            </div>
                            <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6 col-12">
                                <div class="pe-1">
                                    <label for="customerId">Khách hàng:</label>
                                    <select id="customerId" class="w-100"></select>
                                </div>
                            </div>
                            <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6 col-12">
                                <div class="pe-1">
                                    <label for="status">Trạng thái:</label>
                                    <select id="status" class="w-100"></select>
                                </div>
                            </div>
                            <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6 col-12">
                                <div class="pe-1">
                                    <label for="dateFrom">Từ ngày:</label>
                                    <input type="text" class="w-100" id="dateFrom"/>
                                </div>
                            </div>
                            <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6 col-12">
                                <div class="pe-1">
                                    <label for="dateTo">Đến ngày:</label>
                                    <input type="text" class="w-100" id="dateTo"/>
                                </div>
                            </div>
                            <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6 col-12 d-flex align-items-end">
                                <div class="pe-1 d-flex gap-2">
                                    <button id="search" title="Tìm kiếm" class="k-button k-button-md k-rounded-md k-button-solid k-button-solid-primary k-icon-button"><span class='k-icon k-i-search k-button-icon'></span><span class='k-button-text d-none'>Tìm kiếm</span></button>
                                    <button id='create' title="Thêm đơn hàng mới" class='k-button k-button-md k-rounded-md k-button-solid k-button-solid-success _permission_' data-enum='40'><span class='k-icon k-i-plus k-button-icon'></span><span class='k-button-text'>Thêm mới</span></button>
                                    <button id="exportExcel" class="k-button k-button-md k-rounded-md k-button-outline k-button-outline-error"><span class="k-icon k-i-file-excel k-button-icon"></span><span class="k-button-text">Export Excel</span></button>
                                    <button id="bulkExportPOExcel" class="k-button k-button-md k-rounded-md k-button-outline k-button-outline-info" disabled><span class="k-icon k-i-file-excel k-button-icon"></span><span class="k-button-text">Xuất PO Excel</span></button>
                                </div>
                            </div>
                        </div>
                </div>
            `;

        $(gridId).kendoGrid({
            dataSource: {
                transport: {
                    read: {
                        url: "/PurchaseOrder/GetPurchaseOrderList",
                        datatype: "json",
                    },
                    parameterMap: function (data, type) {
                        if (type == "read") {
                            var searchModel = getSearchModel();
                            return {
                                ...searchModel,
                                pageSize: data.pageSize,
                                pageNumber: data.page
                            }
                        }
                    },
                },
                serverPaging: true,
                serverFiltering: true,
                page: 1,
                pageSize: 20,
                schema: {
                    type: 'json',
                    parse: function (response) {
                        if (response.isSuccess == false) {
                            showErrorMessages(response.errorMessageList);
                            return {
                                data: [],
                                total: 0
                            }
                        }
                        return response.data;
                    },
                    model: {
                        id: "id",
                        fields: {
                            createdDate: { type: "date" },
                            updatedDate: { type: "date" },
                            stt: { type: "number" },
                            totalPrice: { type: "number" },
                            totalPriceNoTax: { type: "number" }
                        }
                    },
                    data: "data",
                    total: "total"
                },
            },
            selectable: false,
            scrollable: { virtual: false },
            pageable: {
                pageSizes: [10, 20, 50],
            },
            dataBinding: function (e) {
                record = (this.dataSource._page - 1) * this.dataSource._pageSize;
            },
            toolbar: htmlToolbar,
            detailInit: detailInit,
            columns: [
                {
                    selectable: true,
                    headerSelectable: true,
                    width: "60px !important",
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align: center;" }
                },
                {
                    field: "",
                    title: "STT",
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align: center;" },
                    template: "#: ++record #",
                    width: "60px !important;"
                },
                {
                    field: "code",
                    title: "Mã đơn hàng",
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:center;" },
                    width: "150px",
                },
                {
                    field: "customerName",
                    title: "Khách hàng",
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:left;" },
                    width: "200px",
                },
                {
                    field: "statusName",
                    title: "Trạng thái",
                    width: "130px",
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:center;" },
                    template: function (dataItem) {
                        // Add null check for dataItem
                        if (!dataItem) {
                            return '<span class="badge badge-secondary">N/A</span>';
                        }

                        let statusClass = "";
                        // Status có thể được lưu dưới dạng enum name hoặc enum value
                        switch (dataItem.status) {
                            case "New":
                            case "0":
                                statusClass = "badge-secondary"; break; // Mới
                            case "Confirmed":
                            case "1":
                                statusClass = "badge-success"; break;   // Đã xác nhận
                            case "Executed":
                            case "2":
                                statusClass = "badge-info"; break;      // Đã tạo đơn tổng hợp
                            case "Cancel":
                            case "3":
                                statusClass = "badge-danger"; break;    // Đã hủy
                            default: statusClass = "badge-secondary";
                        }
                        return `<span class="badge ${statusClass}">${dataItem.statusName || 'N/A'}</span>`;
                    }
                },
                {
                    field: "totalPriceNoTax",
                    title: "Tổng tiền chưa thuế",
                    width: "160px",
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:right;" },
                    template: "<span class='currency'>#: kendo.toString(totalPriceNoTax, 'n0') # VNĐ</span>",
                },
                {
                    field: "totalPrice",
                    title: "Tổng tiền",
                    width: "150px",
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:right;" },
                    template: "<span class='currency'>#: kendo.toString(totalPrice, 'n0') # VNĐ</span>",
                },
                {
                    field: "updatedDate",
                    title: "Ngày cập nhật",
                    width: "150px",
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:center;" },
                    template: function (dataItem) {
                        // Display creation date when update date is null (user preference)
                        var dateToShow = dataItem.updatedDate || dataItem.createdDate;
                        return dateToShow ? kendo.toString(kendo.parseDate(dateToShow), "dd/MM/yyyy HH:mm") : '';
                    }
                },
                {
                    field: "",
                    title: "Thao tác",
                    width: "200px",
                    attributes: { style: "text-align: center;" },
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    template: function (dataItem) {
                        var confirmButton = '';
                        var cancelButton = '';

                        // Show confirm button only for orders with status "New" (0)
                        if (dataItem.status === "New" || dataItem.status === "0" || dataItem.status === 0) {
                            confirmButton = '<button onclick="confirmPurchaseOrder(' + dataItem.id + ')" title="Xác nhận đơn hàng" class="btn-action btn-confirm _permission_" data-enum="42">' +
                                '<i class="fas fa-check"></i>' +
                                '</button>';
                        }

                        // Show cancel button only for orders with New (0) and Confirmed (1) statuses
                        if ((dataItem.status === "New" || dataItem.status === "0" || dataItem.status === 0) ||
                            (dataItem.status === "Confirmed" || dataItem.status === "1" || dataItem.status === 1)) {
                            cancelButton = '<button onclick="cancelPurchaseOrder(' + dataItem.id + ')" title="Hủy đơn hàng" class="btn-action btn-cancel _permission_" data-enum="42">' +
                                '<i class="fas fa-ban"></i>' +
                                '</button>';
                        }

                        // Show delete button for orders with New (0) and Confirmed (1) status
                        var deleteButton = '';
                        if (dataItem.status === "New" || dataItem.status === "0" || dataItem.status === 0 ||
                            dataItem.status === "Confirmed" || dataItem.status === "1" || dataItem.status === 1) {
                            deleteButton = '<button onclick="deletePurchaseOrder(' + dataItem.id + ')" title="Xoá" class="btn-action btn-delete _permission_" data-enum="43">' +
                                '<i class="fas fa-trash"></i>' +
                                '</button>';
                        }

                        // Show edit button for orders with New (0) and Confirmed (1) status
                        var editButton = '';
                        if (dataItem.status === "New" || dataItem.status === "0" || dataItem.status === 0 ||
                            dataItem.status === "Confirmed" || dataItem.status === "1" || dataItem.status === 1) {
                            editButton = '<button onclick="editPurchaseOrder(' + dataItem.id + ')" title="Chỉnh sửa" class="btn-action btn-edit _permission_" data-enum="42">' +
                                '<i class="fas fa-edit"></i>' +
                                '</button>';
                        }

                        // Show invoice button only for orders that are not cancelled
                        var invoiceButton = '';
                        if (dataItem.status !== "Cancel" && dataItem.status !== "3" && dataItem.status !== 3) {
                            invoiceButton = '<button class="btn-action btn-invoice" type="button" onclick="toggleInvoiceDropdown(this, ' + dataItem.id + ')" title="Xuất hóa đơn">' +
                                '<i class="fas fa-file-invoice"></i>' +
                                '</button>';
                        }

                        // View details button - always available for all orders
                        var viewDetailsButton = '<button onclick="viewPurchaseOrderDetail(' + dataItem.id + ')" title="Xem chi tiết" class="btn-action btn-view _permission_" data-enum="41">' +
                            '<i class="fas fa-eye"></i>' +
                            '</button>';

                        return '<div class="action-buttons">' +
                            viewDetailsButton +
                            editButton +
                            deleteButton +
                            confirmButton +
                            cancelButton +
                            invoiceButton +
                            '</div>';
                    }
                }
            ],
            dataBound: function (e) {
                CheckPermission();
                loadMobile(gridId);
            },
            change: function(e) {
                // Enable/disable bulk export button based on selection
                var selectedRows = this.select();
                var bulkExportButton = $("#bulkExportPOExcel");
                if (selectedRows.length > 0) {
                    bulkExportButton.prop("disabled", false);
                } else {
                    bulkExportButton.prop("disabled", true);
                }
            }
        });
    }

    function detailInit(e) {
        $("<div/>").appendTo(e.detailCell).kendoGrid({
            dataSource: {
                data: e.data.purchaseOrderItems || [],
                schema: {
                    model: {
                        fields: {
                            quantity: { type: "number" },
                            price: { type: "number" },
                            taxRate: { type: "number" },
                            lossRate: { type: "number" },
                            additionalCost: { type: "number" },
                            processingFee: { type: "number" }
                        }
                    }
                }
            },
            scrollable: false,
            sortable: true,
            pageable: false,
            columns: [
                { field: "productName", title: "Sản phẩm", width: "200px" },
                { field: "quantity", title: "Số lượng", width: "80px", template: "#= '<span class=\"number\">' + kendo.toString(quantity, 'n2') + '</span>' #" },
                { field: "unitName", title: "Đơn vị", width: "80px" },
                { field: "price", title: "Đơn giá", width: "100px", template: "#= price ? '<span class=\"currency\">' + kendo.toString(price, 'n0') + ' VNĐ</span>' : '' #" },
                { field: "taxRate", title: "Thuế suất (%)", width: "80px", template: "#= taxRate ? '<span class=\"number\">' + kendo.toString(taxRate, 'n1') + '%</span>' : '' #" },
                { field: "lossRate", title: "Hao hụt (%)", width: "100px", template: "#= lossRate ? '<span class=\"number\">' + kendo.toString(lossRate, 'n1') + '%</span>' : '' #" },
                {
                    title: "Tổng ước tính nguyên liệu",
                    width: "120px",
                    template: function(dataItem) {
                        var totalEstimatedMaterial = calculateTotalEstimatedMaterial(dataItem.quantity, dataItem.lossRate);
                        return '<span class="number">' + kendo.toString(totalEstimatedMaterial, 'n2') + '</span>';
                    }
                },
                { field: "additionalCost", title: "Chi phí thêm", width: "120px", template: "#= additionalCost ? '<span class=\"currency\">' + kendo.toString(additionalCost, 'n0') + ' VNĐ</span>' : '' #" },
                { field: "processingFee", title: "Phí gia công", width: "120px", template: "#= processingFee ? '<span class=\"currency\">' + kendo.toString(processingFee, 'n0') + ' VNĐ</span>' : '' #" },
                {
                    title: "Thành tiền",
                    width: "150px",
                    template: function(dataItem) {
                        var baseAmount = (dataItem.quantity || 0) * (dataItem.price || 0);
                        var totalProcessingFee = (dataItem.quantity || 0) * (dataItem.processingFee || 0);
                        var totalBeforeTax = baseAmount + (dataItem.additionalCost || 0) + totalProcessingFee; // BỎ lossAmount
                        var taxAmount = totalBeforeTax * ((dataItem.taxRate || 0) / 100);
                        var finalAmount = totalBeforeTax + taxAmount;
                        return '<span class="currency">' + kendo.toString(finalAmount, 'n0') + ' VNĐ</span>';
                    }
                },
                { field: "note", title: "Ghi chú", width: "200px" }
            ]
        });
    }

    function InitKendoToolBar() {
        // Initialize Kendo components
        $("#search").kendoButton({
            icon: "search"
        });
        $("#search").click(async function (e) {
            var grid = $(gridId).data("kendoGrid");
            grid.dataSource.filter({});
        });

        $("#exportExcel").click(async function (e) {
            ExportExcel();
        });

        $("#bulkExportPOExcel").click(async function (e) {
            bulkExportPOExcel();
        });

        $("#searchString").kendoTextBox({
            icon: {
                type: "search",
                position: "end"
            },
            placeholder: "Nhập mã đơn hàng..."
        });


        // Initialize customer dropdown with Kendo DropDownList
        $("#customerId").kendoDropDownList({
            dataTextField: "text",
            dataValueField: "value",
            optionLabel: "-- Chọn khách hàng --",
            template: function (dataItem) {
                if (!dataItem || !dataItem.text) {
                    return "N/A";
                }
                return dataItem.text;
            },
            valueTemplate: function (dataItem) {
                if (!dataItem || !dataItem.text) {
                    return "-- Chọn khách hàng --";
                }
                return dataItem.text;
            },
            dataSource: {
                transport: {
                    read: {
                        url: "/Common/GetDataOptionsDropdown",
                        data: { type: "Customer" }
                    }
                },
                schema: {
                    parse: function (response) {
                        if (response && response.isSuccess && response.data) {
                            return response.data.filter(function (item) {
                                return item && item.text && item.value !== undefined;
                            });
                        }
                        return [];
                    }
                },

            }
        });
        // Initialize status dropdown
        $("#status").kendoDropDownList({
            dataTextField: "text",
            dataValueField: "dataRaw",
            optionLabel: "-- Chọn trạng thái --",
            template: function (dataItem) {
                if (!dataItem || !dataItem.text) {
                    return "N/A";
                }
                return dataItem.text;
            },
            valueTemplate: function (dataItem) {
                if (!dataItem || !dataItem.text) {
                    return "-- Chọn trạng thái --";
                }
                return dataItem.text;
            },
            dataSource: {
                transport: {
                    read: {
                        url: "/Common/GetDataOptionsDropdown",
                        data: { type: "POStatus" }
                    }
                },
                schema: {
                    parse: function (response) {
                        if (response && response.isSuccess && response.data) {
                            return response.data.filter(function (item) {
                                return item && item.text && item.dataRaw !== undefined;
                            });
                        }
                        return [];
                    }
                }
            }
        });

        // Create button click handler
        $("#create").on('click', function () {
            renderPurchaseOrderForm();
        });


        // Initialize Date Pickers
        $("#dateFrom").kendoDatePicker({
            format: "dd/MM/yyyy",
            culture: "vi-VN",
            value: oneWeekAgo
        });

        $("#dateTo").kendoDatePicker({
            format: "dd/MM/yyyy",
            culture: "vi-VN",
            value: today
        });
    }

    // Helper Functions for Purchase Order Form
    function enableProductSelection() {
        var productComboBox = $("#item-productId").data("kendoComboBox");
        if (productComboBox) {
            productComboBox.enable(true);
            // Update placeholder text when enabled
            productComboBox.setOptions({
                placeholder: "Tìm kiếm sản phẩm..."
            });
        }
        // Remove disabled styling and placeholder
        $("#item-productId").removeClass("k-disabled");
        $("#item-productId_wrapper").removeClass("k-disabled");
    }

    function disableProductSelection() {
        var productComboBox = $("#item-productId").data("kendoComboBox");
        if (productComboBox) {
            productComboBox.enable(false);
            productComboBox.value("");
        }
        // Clear product-related fields when disabled
        clearProductFields();
        // Add disabled styling
        $("#item-productId").addClass("k-disabled");
        $("#item-productId_wrapper").addClass("k-disabled");
    }

    function clearProductFields() {
        $("#item-unitName").data("kendoTextBox").value("");
        $("#item-unitName").removeData("unitId");
        $("#item-price").data("kendoNumericTextBox").value("");
        $("#item-taxRate").data("kendoNumericTextBox").value("");
        $("#item-lossRate").data("kendoNumericTextBox").value("");
        $("#item-processingFee").data("kendoNumericTextBox").value("");
        $("#item-additionalCost").data("kendoNumericTextBox").value("0");
        $("#item-totalAmount").data("kendoTextBox").value("");
        $("#item-quantity").data("kendoNumericTextBox").value("");
        $("#item-note").data("kendoTextBox").value("");
    }

    function loadCustomerDetails(customerId) {
        ajax("GET", "/Customer/GetCustomerById/" + customerId, {}, function (response) {
            if (response.isSuccess && response.data) {
                var customer = response.data;
                // Store customer type for tax rate calculation
                $("#po-customerId").data("customerType", customer.type);

                // Auto-populate payment fields from customer data
                {
                    $("#po-customerAccountNumber").data("kendoTextBox").value(customer.accountNumber || null);
                }

                $("#po-customerBankName").data("kendoTextBox").value(customer.bankName || null)

                {
                    var paymentMethodDropdown = $("#po-paymentMethod").data("kendoDropDownList");
                    if (paymentMethodDropdown) {
                        paymentMethodDropdown.value(customer.paymentMethod || null);
                    }
                }

                // Update tax rate for currently selected product in form
                var productComboBox = $("#item-productId").data("kendoComboBox");
                if (productComboBox && productComboBox.value()) {
                    var selectedProductId = productComboBox.value();
                    // Reload product details to recalculate tax rate with new customer type
                    loadProductDetails(selectedProductId);
                }

                // Update tax rates for all products in the grid
                updateAllGridItemsTaxRates();
            }
            // No notification - silent operation
        }, function (error) {
            // Silent error handling - no notification
        }, false);
    }

    function updateAllGridItemsTaxRates() {
        var grid = $("#itemsGrid").data("kendoGrid");
        if (!grid) return;

        var items = grid.dataSource.data();
        var hasUpdates = false;

        // Process each item in the grid
        for (var i = 0; i < items.length; i++) {
            var item = items[i];
            var productId = item.productId;

            // Get product details to calculate new tax rate
            (function(gridItem) {
                ajax("GET", "/Product/GetProductById/" + productId, {}, function (response) {
                    if (response.isSuccess && response.data) {
                        var product = response.data;
                        var newTaxRate = getTaxRateBasedOnCustomer(product);

                        // Update the grid item if tax rate changed
                        if (gridItem.taxRate !== newTaxRate) {
                            gridItem.set("taxRate", newTaxRate);
                            gridItem.set("totalAmount", calculateItemTotal(gridItem));
                            hasUpdates = true;
                        }
                    }
                }, null, false);
            })(item);
        }

        // Update totals after all items are processed
        setTimeout(function() {
            updatePurchaseOrderTotals();
        }, 500);
    }

    function getTaxRateBasedOnCustomer(product) {
        var customerType = $("#po-customerId").data("customerType");

        if (customerType === undefined || customerType === null) {
            return 0; // No customer selected, default to 0
        }

        // Handle both string and numeric customer types
        // ECustomerType.Company = 0 or "Company", ECustomerType.Individual = 1 or "Individual"
        if (customerType === 0 || customerType === "Company" || customerType.toString().toLowerCase() === "company") {
            return product.companyTaxRate || 0;
        } else if (customerType === 1 || customerType === "Individual" || customerType.toString().toLowerCase() === "individual") {
            return product.consumerTaxRate || 0;
        }

        return 0; // Default fallback
    }

    function loadProductDetails(productId) {
        ajax("GET", "/Product/GetProductById/" + productId, {}, function (response) {
            if (response.isSuccess && response.data) {
                var product = response.data;
                $("#item-unitName").data("kendoTextBox").value(product.unitName || '');
                $("#item-unitName").data("unitId", product.unit_ID || null); // Store unit_ID for later use
                $("#item-price").data("kendoNumericTextBox").value(product.defaultPrice || 0); // Đơn giá = defaultPrice

                // Set tax rate based on customer type and product tax rates
                var taxRate = getTaxRateBasedOnCustomer(product);
                $("#item-taxRate").data("kendoNumericTextBox").value(taxRate);

                // Auto-fill the requested fields
                $("#item-lossRate").data("kendoNumericTextBox").value(product.lossRate || 0);
                $("#item-processingFee").data("kendoNumericTextBox").value(product.processingFee || 0);
                $("#item-additionalCost").data("kendoNumericTextBox").value(product.additionalCost || 0);

                // Calculate total amount when product is loaded
                calculateTotalAmount();
            } else {
                // Show error notification
                showErrorMessages(response.errorMessageList || ["Không thể tải thông tin sản phẩm"]);
            }
        }, function (error) {
            // Handle AJAX error
            showErrorMessages(["Lỗi khi tải thông tin sản phẩm"]);
        }, false);
    }

    // Function to calculate total amount with new formula
    function calculateTotalAmount() {
        var quantity = parseFloat($("#item-quantity").val()) || 0;
        var price = parseFloat($("#item-price").val()) || 0;
        var additionalCost = parseFloat($("#item-additionalCost").val()) || 0;
        var processingFee = parseFloat($("#item-processingFee").val()) || 0;
        var taxRate = parseFloat($("#item-taxRate").val()) || 0;

        // Bước 1: Tính tiền cơ bản (Số lượng × Đơn giá)
        var baseAmount = quantity * price;

        // Bước 2: Tính phí gia công (Số lượng × Phí gia công đơn vị)
        var totalProcessingFee = quantity * processingFee;

        // Bước 3: Tính tổng trước thuế (tiền cơ bản + chi phí thêm + phí gia công) - BỎ lossAmount
        var totalBeforeTax = baseAmount + additionalCost + totalProcessingFee;

        // Bước 4: Tính tiền thuế (% thuế × tổng trước thuế)
        var taxAmount = totalBeforeTax * (taxRate / 100);

        // Bước 5: Tổng cuối cùng (tổng trước thuế + thuế)
        var finalAmount = totalBeforeTax + taxAmount;

        $("#item-totalAmount").data("kendoTextBox").value(finalAmount.toFixed(0));
    }

    function addItemToGrid() {
        var productDropdown = $("#item-productId").data("kendoComboBox");

        var productId = productDropdown ? productDropdown.value() : "";
        var productName = productDropdown ? productDropdown.text() : "";
        var quantity = parseFloat($("#item-quantity").val()) || 0;
        var unitName = $("#item-unitName").val();
        var unitId = $("#item-unitName").data("unitId"); // Get stored unit_ID
        var additionalCost = parseFloat($("#item-additionalCost").val()) || 0;
        var price = parseFloat($("#item-price").val()) || 0;
        var lossRate = parseFloat($("#item-lossRate").val()) || 0;
        var taxRate = parseFloat($("#item-taxRate").val()) || 0;
        var processingFee = parseFloat($("#item-processingFee").val()) || 0;
        var totalAmount = parseFloat($("#item-totalAmount").val()) || 0;
        var note = $("#item-note").val();

        // Validation
        if (!productId || productId === "" || productName === "-- Chọn sản phẩm --") {
            showErrorMessages(["Vui lòng chọn sản phẩm"]);
            return;
        }
        if (quantity <= 0) {
            showErrorMessages(["Vui lòng nhập số lượng hợp lệ"]);
            return;
        }

        // Use the calculated total amount from the form (already calculated by calculateTotalAmount)
        var calculatedTotalAmount = parseFloat($("#item-totalAmount").val()) || 0;

        var newItem = {
            tempId: generateTempId(),
            productId: parseInt(productId),
            productName: productName,
            quantity: quantity,
            unitName: unitName,
            unitId: unitId, // Include unit_ID
            price: price,
            lossRate: lossRate,
            taxRate: taxRate,
            additionalCost: additionalCost,
            processingFee: processingFee,
            totalAmount: calculatedTotalAmount,
            note: note
        };

        var grid = $("#itemsGrid").data("kendoGrid");
        grid.dataSource.add(newItem);

        // Clear form
        clearItemForm();

        // Update totals
        updatePurchaseOrderTotals();
    }

    function removeItem(tempId) {
        var grid = $("#itemsGrid").data("kendoGrid");
        var dataItem = grid.dataSource.get(tempId);
        if (dataItem) {
            grid.dataSource.remove(dataItem);
            updatePurchaseOrderTotals();
        }
    }

    function clearItemForm() {
        var productComboBox = $("#item-productId").data("kendoComboBox");
        if (productComboBox) {
            productComboBox.value("");
        }


        $("#item-unitName").data("kendoTextBox").value("");
        $("#item-unitName").removeData("unitId");
        $("#item-price").data("kendoNumericTextBox").value("");
        $("#item-taxRate").data("kendoNumericTextBox").value("");
        $("#item-lossRate").data("kendoNumericTextBox").value("");
        $("#item-processingFee").data("kendoNumericTextBox").value("");
        $("#item-additionalCost").data("kendoNumericTextBox").value("0");
        $("#item-totalAmount").data("kendoTextBox").value("");
        $("#item-quantity").data("kendoNumericTextBox").value("");
        $("#item-note").data("kendoTextBox").value("");
    }

    function updatePurchaseOrderTotals() {
        var grid = $("#itemsGrid").data("kendoGrid");
        var items = grid.dataSource.data();

        var totalPriceNoTax = 0;
        var totalPrice = 0;
        for (var i = 0; i < items.length; i++) {
            var item = items[i];
            // Bước 1: Tính tiền cơ bản (Số lượng × Đơn giá)
            var baseAmount = item.quantity * item.price;

            // Bước 2: Tính phí gia công (Số lượng × Phí gia công đơn vị)
            var totalProcessingFee = item.quantity * (item.processingFee || 0);

            // Bước 3: Tính tổng trước thuế (tiền cơ bản + chi phí thêm + phí gia công) - BỎ lossAmount
            var totalBeforeTax = baseAmount + (item.additionalCost || 0) + totalProcessingFee;

            // Bước 4: Tính tiền thuế (% thuế × tổng trước thuế)
            var taxAmount = totalBeforeTax * ((item.taxRate || 0) / 100);

            // Cộng dồn vào tổng
            totalPriceNoTax += totalBeforeTax;
            totalPrice += totalBeforeTax + taxAmount;
        }

        $("#po-totalPriceNoTax").data("kendoTextBox").value(kendo.toString(totalPriceNoTax, "n0") + " VNĐ");
        $("#po-totalPrice").data("kendoTextBox").value(kendo.toString(totalPrice, "n0") + " VNĐ");
    }

    function generateTempId() {
        return 'temp_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    function calculateItemTotal(item) {
        var baseAmount = (item.quantity || 0) * (item.price || 0);
        var totalProcessingFee = (item.quantity || 0) * (item.processingFee || 0);
        var totalBeforeTax = baseAmount + (item.additionalCost || 0) + totalProcessingFee; // BỎ lossAmount
        var taxAmount = totalBeforeTax * ((item.taxRate || 0) / 100);
        return totalBeforeTax + taxAmount;
    }

    function loadPurchaseOrderData(data) {
        // Load header data
        try {
            if (data.customer_ID) {
                var customerDropdown = $("#po-customerId").data("kendoDropDownList");
                if (customerDropdown) {
                    customerDropdown.value(data.customer_ID);
                    // Load customer details for tax rate calculation
                    loadCustomerDetails(data.customer_ID);
                    // Enable product selection since customer is loaded
                    enableProductSelection();
                }
            }

            if (data.status !== undefined) {
                var statusDropdown = $("#po-status").data("kendoDropDownList");
                if (statusDropdown) {
                    statusDropdown.value(data.status);
                }
            }

            $("#po-code").val(data.code || '');
            $("#po-id").val(data.id || 0);
            $("#po-customerAccountNumber").val(data.customerAccountNumber || '');
            $("#po-customerBankName").val(data.customerBankName || '');

            // Set payment method dropdown value
            if (data.paymentMethod) {
                var paymentMethodDropdown = $("#po-paymentMethod").data("kendoDropDownList");
                if (paymentMethodDropdown) {
                    paymentMethodDropdown.value(data.paymentMethod);
                }
            }

            // Load items
            if (data.purchaseOrderItems && data.purchaseOrderItems.length > 0) {
                var grid = $("#itemsGrid").data("kendoGrid");
                if (grid) {
                    // Clear existing data first
                    grid.dataSource.data([]);

                    for (var i = 0; i < data.purchaseOrderItems.length; i++) {
                        var item = data.purchaseOrderItems[i];
                        // Map server field names to client field names
                        var gridItem = {
                            tempId: generateTempId(),
                            productId: item.product_ID || item.productId,
                            productName: item.productName,
                            quantity: item.quantity,
                            unitName: item.unitName,
                            unitId: item.unit_ID || item.unitId, // Include unit_ID for existing items
                            price: item.price,
                            lossRate: item.lossRate,
                            taxRate: item.taxRate,
                            additionalCost: item.additionalCost || 0,
                            processingFee: item.processingFee,
                            note: item.note,
                            totalAmount: calculateItemTotal(item)
                        };
                        grid.dataSource.add(gridItem);
                    }
                    updatePurchaseOrderTotals();
                }
            }

            // Load totals
            if (data.totalPriceNoTax !== undefined) {
                $("#po-totalPriceNoTax").data("kendoTextBox").value(kendo.toString(data.totalPriceNoTax, "n0") + " VNĐ");
            }
            if (data.totalPrice !== undefined) {
                $("#po-totalPrice").data("kendoTextBox").value(kendo.toString(data.totalPrice, "n0") + " VNĐ");
            }

        } catch (error) {
            // Error loading PO data
        }
    }

    function savePurchaseOrder() {
        var customerDropdown = $("#po-customerId").data("kendoDropDownList");
        var statusDropdown = $("#po-status").data("kendoDropDownList");
        var grid = $("#itemsGrid").data("kendoGrid");

        var customerId = customerDropdown.value();
        var status = statusDropdown.value();
        var items = grid.dataSource.data();

        // Validation
        if (!customerId || customerId === "") {
            showErrorMessages(["Vui lòng chọn khách hàng"]);
            return;
        }

        if (items.length === 0) {
            showErrorMessages(["Vui lòng thêm ít nhất một sản phẩm"]);
            return;
        }

        // Check if this is an edit operation by looking for existing PO ID
        var poId = parseInt($("#po-id").val()) || 0;
        var isEdit = poId > 0;

        // Prepare data

        var purchaseOrderData = {
            id: poId,
            customer_ID: parseInt(customerId),
            status: status || 0,
            customerAccountNumber: $("#po-customerAccountNumber").data("kendoTextBox").value() || '',
            customerBankName: $("#po-customerBankName").data("kendoTextBox").value() || '',
            paymentMethod: $("#po-paymentMethod").data("kendoDropDownList").value() || null,
            purchaseOrderItems: items.map(function (item) {
                return {
                    id: 0, // Always 0 for new items (will be handled by server)
                    po_ID: poId, // Set the PO ID for existing orders
                    product_ID: item.productId,
                    quantity: item.quantity,
                    unit_ID: item.unitId, // Use captured unit_ID from product
                    price: item.price,
                    taxRate: item.taxRate,
                    lossRate: item.lossRate,
                    additionalCost: item.additionalCost || 0,
                    processingFee: item.processingFee,
                    note: item.note
                };
            }),
        };

        // Determine URL and method based on operation type
        var url = isEdit ? "/PurchaseOrder/UpdatePurchaseOrder" : "/PurchaseOrder/CreatePurchaseOrder";
        var method = isEdit ? "PUT" : "POST";
        var successMessage = isEdit ? "Cập nhật đơn hàng thành công!" : "Lưu đơn hàng thành công!";
        debugger
        ajax(method, url, purchaseOrderData, function (response) {
            if (response.isSuccess) {
                showSuccessMessages([successMessage]);
                $("#window").data("kendoWindow").close();
                $(gridId).data("kendoGrid").dataSource.read();
            }
        }, null, false);
    }
</script>

<script type="text/javascript">
    InitGrid();
    InitKendoToolBar();
    $(document).ready(function () {
        $(window).trigger("resize");

        // Add event listeners for automatic total amount calculation
        $(document).on('input', '#item-quantity, #item-price, #item-processingFee, #item-additionalCost, #item-lossRate, #item-taxRate', function () {
            calculateTotalAmount();
        });

        // Auto load data with default filter (last 1 week)
        // setTimeout(function () {

        //     $(window).trigger("resize");
        // }, 500);
    });
</script>

<style>
    .k-form-buttons {
        justify-content: flex-end;
    }

    /* Page specific overrides */
    .purchase-order-toolbar {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    /* Custom dropdown overlay styling */
    .invoice-dropdown-overlay {
        font-family: inherit;
        line-height: 1.5;
    }

    .invoice-dropdown-overlay a {
        transition: background-color 0.15s ease-in-out;
    }

    .invoice-dropdown-overlay a:hover {
        background-color: #f8f9fa !important;
    }

    /* Confirm button styling */
    .btn-action.btn-confirm {
        background-color: #28a745;
        color: white;
        border: 1px solid #28a745;
    }

    .btn-action.btn-confirm:hover {
        background-color: #218838;
        border-color: #1e7e34;
    }

    /* Force 4-column layout for item input */
    .item-input-grid .grid-row-4col {
        display: grid !important;
        grid-template-columns: repeat(4, 1fr) !important;
        gap: 15px !important;
        margin-bottom: 15px;
        align-items: end;
    }

    .grid-row-4col {
        display: grid !important;
        grid-template-columns: repeat(4, 1fr) !important;
        gap: 15px !important;
        margin-bottom: 15px;
        align-items: end;
    }

    .grid-col-4-1 {
        grid-column: span 1 !important;
    }

    .grid-col-4-2 {
        grid-column: span 2 !important;
    }

    .grid-col-4-3 {
        grid-column: span 3 !important;
    }

    .grid-row-button {
        display: flex !important;
        justify-content: flex-end !important;
        margin-top: 20px;
        padding-top: 15px;
        border-top: 1px solid #dee2e6;
    }

    .grid-row-button .form-group {
        margin-bottom: 0;
    }

    .grid-row-button .btn {
        padding: 8px 16px;
        font-size: 13px;
        font-weight: 500;
    }

    /* 3-Column Grid Layout for Header */
    .header-grid-3col {
        display: grid !important;
        grid-template-columns: repeat(3, 1fr) !important;
        gap: 20px !important;
        margin-bottom: 0;
        align-items: end;
    }

    .header-col-3-1 {
        grid-column: span 1 !important;
    }

    .header-col-3-2 {
        grid-column: span 2 !important;
    }

    .header-col-3-3 {
        grid-column: span 3 !important;
    }

    /* Responsive for header grid */
    @@media (max-width: 992px) {
        .header-grid-3col {
            grid-template-columns: repeat(2, 1fr) !important;
        }
    }

    @@media (max-width: 768px) {
        .header-grid-3col {
            grid-template-columns: 1fr !important;
            gap: 15px !important;
        }
    }

    /* Disabled status dropdown styling */
    .k-dropdown.k-disabled {
        background-color: #f8f9fa !important;
        border-color: #e9ecef !important;
        opacity: 0.8;
        cursor: not-allowed;
    }

    .k-dropdown.k-disabled .k-dropdown-wrap {
        background-color: #f8f9fa !important;
        color: #6c757d !important;
        cursor: not-allowed;
    }

    .k-dropdown.k-disabled .k-select {
        background-color: #f8f9fa !important;
        border-color: #e9ecef !important;
        cursor: not-allowed;
    }

    /* Add a subtle indicator that status is read-only */
    .k-dropdown.k-disabled::after {
        content: "🔒";
        position: absolute;
        right: 25px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 12px;
        opacity: 0.6;
        pointer-events: none;
    }

    /* Enhanced styling for header section */
    .po-header-section {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .po-header-section h5 {
        color: #096b73;
        margin-bottom: 20px;
        font-weight: 600;
        border-bottom: 2px solid #096b73;
        padding-bottom: 8px;
    }

    /* Disabled product selection styling */
    .k-combobox.k-disabled {
        background-color: #f8f9fa !important;
        border-color: #e9ecef !important;
        opacity: 0.7;
        cursor: not-allowed;
    }

    .k-combobox.k-disabled .k-dropdown-wrap {
        background-color: #f8f9fa !important;
        color: #6c757d !important;
        cursor: not-allowed;
    }

    .k-combobox.k-disabled .k-select {
        background-color: #f8f9fa !important;
        border-color: #e9ecef !important;
        cursor: not-allowed;
    }

    /* Add visual indicator for disabled customer dropdown in edit mode */
    .k-dropdown.k-disabled::before {
        content: "🔒 Không thể thay đổi trong chế độ chỉnh sửa";
        position: absolute;
        top: -20px;
        left: 0;
        font-size: 11px;
        color: #6c757d;
        font-style: italic;
        white-space: nowrap;
    }

    /* Enhanced styling for item input section */
    .po-item-section {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .po-item-section h5 {
        color: #096b73;
        margin-bottom: 20px;
        font-weight: 600;
        border-bottom: 2px solid #096b73;
        padding-bottom: 8px;
    }

    /* Highlight total amount field */
    .total-amount-highlight {
        background-color: #e8f5e8 !important;
        border: 2px solid #28a745 !important;
        font-weight: bold;
        font-size: 14px;
    }

    .total-amount-highlight:focus {
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
    }

    /* Enhanced form group spacing */
    .item-input-grid .form-group {
        margin-bottom: 15px;
    }

    .item-input-grid .form-group label {
        font-weight: 500;
        color: #495057;
        margin-bottom: 5px;
    }

    /* Required field indicator */
    .item-input-grid .form-group label:contains("(*)") {
        color: #dc3545;
    }

    /* Button styling in grid */
    .grid-row-4col .btn {
        height: 38px;
        font-weight: 500;
    }

    /* Button row styling */
    .grid-row-button {
        margin-top: 20px;
        padding-top: 15px;
        border-top: 1px solid #e9ecef;
    }

    .grid-row-button .btn {
        /* Match the size of form action buttons (Lưu/Hủy) */
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
        line-height: 1.5;
        border-radius: 0.25rem;
        font-weight: 400;
        transition: all 0.2s ease;
    }

    .grid-row-button .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    /* Responsive adjustments for item input */
    @@media (max-width: 1200px) {
        .grid-row-4col {
            grid-template-columns: repeat(2, 1fr) !important;
            gap: 10px !important;
        }

        .grid-col-4-1,
        .grid-col-4-2 {
            grid-column: span 1 !important;
        }
    }

    @@media (max-width: 768px) {
        .grid-row-4col {
            grid-template-columns: 1fr !important;
        }

        .po-item-section {
            padding: 15px;
        }
    }

    /* Purchase Order Detail Modal Styling - Similar to PAGroup */
    .po-detail-container {
        padding: 20px;
        background-color: #f8f9fa;
        border-radius: 8px;
        margin: 10px 0;
    }

    .po-detail-container h6 {
        color: #495057;
        margin-bottom: 15px;
        border-bottom: 1px solid #dee2e6;
        padding-bottom: 8px;
        font-size: 16px;
        font-weight: 600;
    }

    .po-items-detail {
        margin-top: 15px;
    }

    .po-items-detail h6 {
        color: #6c757d;
        font-size: 14px;
        margin-bottom: 10px;
        font-weight: 600;
    }

    .po-info-row {
        display: flex;
        gap: 20px;
        margin-bottom: 15px;
    }

    .po-info-col {
        flex: 1;
        min-width: 0;
    }

    .po-info-col p {
        margin-bottom: 8px;
        word-wrap: break-word;
        font-size: 14px;
    }

    /* Status badge styling for Purchase Orders */
    .status-badge {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 500;
        text-align: center;
        display: inline-block;
        min-width: 80px;
    }

    .status-badge.new {
        background-color: #007bff;
        color: white;
    }

    .status-badge.confirmed {
        background-color: #28a745;
        color: white;
    }

    .status-badge.sent {
        background-color: #17a2b8;
        color: white;
    }

    .status-badge.cancel {
        background-color: #dc3545;
        color: white;
    }

    /* Grid checkbox column styling */
    th.k-hierarchy-cell, td.k-hierarchy-cell {
        width: 50px !important;
        max-width: 50px !important;
        min-width: 30px !important;
        text-align: center !important;
        vertical-align: middle !important;
        padding: 4px 2px !important;
    } 
    /* #gridId .k-grid-header th:first-child,
    #gridId .k-grid-content td:first-child {
        width: 50px !important;
        max-width: 50px !important;
        min-width: 30px !important;
        text-align: center !important;
        vertical-align: middle !important;
        padding: 4px 2px !important;
    } */

    #gridId .k-grid-header th:first-child .k-checkbox,
    #gridId .k-grid-content td:first-child .k-checkbox {
        margin: 0 auto !important;
        display: block !important;
    }

    /* Force table layout for better column control */
    #gridId .k-grid-header table,
    #gridId .k-grid-content table {
        table-layout: fixed !important;
    }

    /* Responsive adjustments for PO detail modal */
    @@media (max-width: 768px) {
        .po-info-row {
            flex-direction: column;
            gap: 10px;
        }

        .po-detail-container {
            padding: 15px;
        }
    }
</style>
